# GT1158/GT9xx 触摸芯片驱动库

## 概述

这是一个功能完整的GT1158/GT9xx系列触摸芯片驱动库，支持多点触摸、手势识别和横竖屏动态切换。该驱动库专为STM32平台设计，提供了简洁易用的API接口。

## 功能特性

### 🎯 核心功能
- **多点触摸支持**：最多支持5个触摸点同时检测
- **手势识别**：支持单击、长按、上滑、下滑、左滑、右滑
- **横竖屏动态切换**：自动适配LCD方向变化，坐标自动转换
- **防误触机制**：边缘检测、防抖处理、最小移动距离检测
- **中断驱动**：基于GPIO中断的高效触摸检测

### 🔧 技术特性
- **I2C通信**：使用I2C2接口，支持重试和超时机制
- **坐标转换**：自动处理横竖屏坐标系统转换
- **事件回调**：提供丰富的事件回调函数，支持用户自定义
- **配置灵活**：支持运行时重新配置屏幕参数

## 硬件连接

| 信号 | STM32引脚 | 功能 |
|------|-----------|------|
| SDA  | PB11      | I2C2数据线 |
| SCL  | PB10      | I2C2时钟线 |
| RST  | PI8       | 复位信号 |
| INT  | PD13      | 中断信号 |
| VCC  | 3.3V      | 电源 |
| GND  | GND       | 地线 |

## 快速开始

### 1. 基础初始化

```c
#include "touch.h"

int main(void)
{
    // 系统初始化
    HAL_Init();
    SystemClock_Config();
    MX_GPIO_Init();
    MX_I2C2_Init();
    
    // LCD初始化
    LCD_Init();
    
    // 触摸初始化（自动配置）
    touch_result_t result = Touch_Init();
    if (result != TOUCH_RESULT_OK) {
        // 初始化失败处理
        Error_Handler();
    }
    
    // 主循环
    while (1) {
        // 处理触摸事件
        Touch_ProcessEvents();
        HAL_Delay(10);
    }
}
```

### 2. 手势检测

```c
// 等待手势输入
touch_gesture_type_t gesture = Touch_WaitForInput(3000);  // 3秒超时

switch (gesture) {
    case TOUCH_GESTURE_CLICK:
        printf("单击检测到\n");
        break;
    case TOUCH_GESTURE_SWIPE_UP:
        printf("上滑检测到\n");
        break;
    case TOUCH_GESTURE_SWIPE_DOWN:
        printf("下滑检测到\n");
        break;
    case TOUCH_GESTURE_SWIPE_LEFT:
        printf("左滑检测到\n");
        break;
    case TOUCH_GESTURE_SWIPE_RIGHT:
        printf("右滑检测到\n");
        break;
    case TOUCH_GESTURE_NONE:
        printf("超时，无手势\n");
        break;
}
```

### 3. 横竖屏切换

```c
// 切换到竖屏
LCD_SetOrientation(0);
Touch_ReconfigureForLCD();

// 切换到横屏
LCD_SetOrientation(1);
Touch_ReconfigureForLCD();
```

## API参考

### 初始化和配置

| 函数 | 功能 | 返回值 |
|------|------|--------|
| `Touch_Init()` | 一键初始化触摸系统 | `touch_result_t` |
| `Touch_InitAdvanced()` | 高级初始化，支持自定义参数 | `touch_result_t` |
| `Touch_Config()` | 配置屏幕参数 | `touch_result_t` |
| `Touch_ReconfigureForLCD()` | 根据LCD配置重新配置 | `touch_result_t` |

### 触摸数据读取

| 函数 | 功能 | 返回值 |
|------|------|--------|
| `Touch_GetSinglePoint()` | 获取单点触摸坐标 | `touch_result_t` |
| `Touch_GetMultiPoint()` | 获取多点触摸数据 | `touch_result_t` |
| `Touch_ProcessEvents()` | 处理触摸事件 | `void` |

### 手势检测

| 函数 | 功能 | 返回值 |
|------|------|--------|
| `Touch_WaitForInput()` | 阻塞式等待手势输入 | `touch_gesture_type_t` |
| `Touch_SetGestureDetection()` | 启用/禁用手势检测 | `touch_result_t` |
| `Touch_GetGestureEvent()` | 获取手势事件 | `touch_result_t` |

### 坐标处理

| 函数 | 功能 | 返回值 |
|------|------|--------|
| `Touch_IsCoordinateInRange()` | 验证坐标是否有效 | `uint8_t` |
| `Touch_ClampCoordinates()` | 限制坐标在屏幕范围内 | `touch_result_t` |
| `Touch_GetScreenInfo()` | 获取当前屏幕配置 | `touch_result_t` |

## 事件回调

驱动提供了丰富的事件回调函数，用户可以重新实现这些函数来自定义行为：

```c
// 触摸按下事件
void Touch_OnPress(uint16_t x, uint16_t y) {
    printf("触摸按下: (%d, %d)\n", x, y);
}

// 触摸释放事件
void Touch_OnRelease(uint16_t x, uint16_t y) {
    printf("触摸释放: (%d, %d)\n", x, y);
}

// 单击事件
void Touch_OnClick(uint16_t x, uint16_t y) {
    printf("单击: (%d, %d)\n", x, y);
}

// 长按事件
void Touch_OnLongPress(uint16_t x, uint16_t y, uint32_t duration_ms) {
    printf("长按: (%d, %d), 持续时间: %lums\n", x, y, duration_ms);
}

// 滑动事件
void Touch_OnSwipe(touch_gesture_type_t gesture_type, 
                   uint16_t start_x, uint16_t start_y, 
                   uint16_t end_x, uint16_t end_y) {
    printf("滑动: 从(%d,%d)到(%d,%d)\n", start_x, start_y, end_x, end_y);
}
```

## 配置参数

### 触摸检测参数

```c
#define TOUCH_EDGE_THRESHOLD_PX     5       // 边缘防误触阈值(像素)
#define TOUCH_MOVE_THRESHOLD_PX     10      // 移动检测阈值(像素)
#define TOUCH_SWIPE_THRESHOLD_PX    50      // 滑动检测阈值(像素)
#define TOUCH_SWIPE_MIN_SPEED       100     // 最小滑动速度(像素/秒)
#define TOUCH_LONG_PRESS_TIME_MS    800     // 长按时间阈值(毫秒)
#define TOUCH_DEBOUNCE_TIME_MS      20      // 防抖时间(毫秒)
#define TOUCH_CLICK_MAX_TIME_MS     500     // 单击最大时间(毫秒)
```

### 通信参数

```c
#define GT1158_I2C_ADDR             0x28    // I2C设备地址
#define GT1158_I2C_RETRY_COUNT      5       // 重试次数
#define GT1158_I2C_TIMEOUT_MS       1000    // 超时时间(毫秒)
```

## 坐标系统

### 横屏模式（LCD方向1）
- 显示区域：800x480
- 起始点(0,0)：左上角
- X增加方向：左→右
- Y增加方向：上→下

### 竖屏模式（LCD方向0）
- 显示区域：480x800
- 起始点(0,0)：横屏状态下的左下角位置
- X增加方向：横屏的下→上
- Y增加方向：横屏的左→右

## 故障排除

### 常见问题

1. **触摸无响应**
   - 检查I2C连接
   - 确认中断引脚配置
   - 检查电源供应

2. **坐标不准确**
   - 调用`Touch_ReconfigureForLCD()`重新配置
   - 检查LCD方向设置
   - 验证屏幕尺寸配置

3. **手势识别不准确**
   - 调整检测阈值参数
   - 检查防误触设置
   - 确认触摸区域不在边缘

### 调试工具

使用提供的测试函数进行调试：

```c
// 基础坐标测试
LCD_TouchCoordinateTest();

// 坐标转换调试
LCD_TouchTransformDebugTest();

// 横竖屏切换测试
LCD_TouchOrientationSwitchTest();
```

## 版本历史

- **V4.0** (2025-01-27): 支持横竖屏动态切换，优化坐标转换
- **V3.1** (2025-01-25): 增加手势识别功能
- **V3.0** (2024-12-15): 重构代码结构，提高稳定性
- **V2.0** (2024-11-20): 添加多点触摸支持
- **V1.0** (2024-10-10): 初始版本

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个驱动库。

## 联系方式

如有问题或建议，请联系开发团队。
