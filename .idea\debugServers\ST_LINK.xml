<component name="DebugServers">
  <stlink-debug-target name="ST-LINK" uniqueID="efab069e-2b96-4b10-a49a-04ce7ecb1306" selected="true">
    <debugger version="1">
      <debugger kind="GDB" isBundled="true" />
      <env />
    </debugger>
    <gdbserver exe="F:\STM32cube\STM32cubeide\STM32CubeCLT_1.18.0\STLink-gdb-server\bin\ST-LINK_gdbserver.exe" programmer="F:\STM32cube\STM32cubeide\STM32CubeCLT_1.18.0\STM32CubeProgrammer\bin" />
    <st-link />
    <device interface="SWD" />
    <connection extended-remote="false" port="61234" warmup-ms="500" />
    <swo enabled="false" port="61235" />
  </stlink-debug-target>
</component>