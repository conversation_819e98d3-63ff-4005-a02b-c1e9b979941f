{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.22"}, "projectIndex": 0, "source": ".", "targetIndexes": [1]}, {"build": "cmake/stm32cubemx", "jsonFile": "directory-cmake.stm32cubemx-Debug-a874d3cc84b5982fda2e.json", "minimumCMakeVersion": {"string": "3.22"}, "parentIndex": 0, "projectIndex": 0, "source": "cmake/stm32cubemx", "targetIndexes": [0, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1], "name": "Fatfs", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 1, "id": "FatFs::@768a070a0fe75716b479", "jsonFile": "target-FatFs-Debug-c6563b222970635c0a36.json", "name": "FatFs", "projectIndex": 0}, {"directoryIndex": 0, "id": "Fatfs::@6890427a1f51a3e7e1df", "jsonFile": "target-Fatfs-Debug-efda6e3574bfbaf753af.json", "name": "Fatfs", "projectIndex": 0}, {"directoryIndex": 1, "id": "STM32_Drivers::@768a070a0fe75716b479", "jsonFile": "target-STM32_Drivers-Debug-a347f58bec174728fc8a.json", "name": "STM32_Drivers", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "source": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs"}, "version": {"major": 2, "minor": 7}}