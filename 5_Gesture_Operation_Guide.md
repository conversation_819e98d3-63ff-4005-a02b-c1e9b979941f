# FatFS 5-Gesture Operation Guide

## 手势操作说明

本系统仅使用5种基本手势操作：
- **上滑 (Swipe Up)**
- **下滑 (Swipe Down)**  
- **左滑 (Swipe Left)**
- **右滑 (Swipe Right)**
- **点击 (Click)**

## 主菜单操作

### LCD显示内容
```
=== FatFS 4K Optimized Test ===
[Swipe Up] Disk Info Test
[Swipe Down] File Operations
[Swipe Left] 4K Aligned R/W
[Swipe Right] Batch Operations
[Click] More Options...
Please select test item...

System: FatFS 4K Optimized v1.0
Disk Usage: 15%
```

### 手势功能
| 手势 | 功能 | 说明 |
|------|------|------|
| **上滑** | 磁盘信息测试 | 显示FAT类型、空间使用情况等 |
| **下滑** | 文件操作测试 | 测试创建、复制、移动文件等 |
| **左滑** | 4K对齐读写测试 | 测试4K对齐的读写性能 |
| **右滑** | 批量操作测试 | 测试批量文件处理功能 |
| **点击** | 进入更多选项 | 进入子菜单 |

## 子菜单操作

### LCD显示内容
```
=== FatFS More Options ===
[Swipe Up] Performance Test
[Swipe Down] Format Disk
[Swipe Left] Clean All Files
[Swipe Right] Exit Test
[Click] Back to Main
Please select operation...
```

### 手势功能
| 手势 | 功能 | 说明 |
|------|------|------|
| **上滑** | 性能测试 | 测试读写速度和性能指标 |
| **下滑** | 格式化磁盘 | 格式化QSPI Flash |
| **左滑** | 清理所有文件 | 删除Flash中的所有文件和目录 |
| **右滑** | 退出测试 | 退出整个测试程序 |
| **点击** | 返回主菜单 | 回到主菜单界面 |

## 操作流程

### 1. 系统启动
```
FatFS 4K Optimized Test System
FatFS OK
Disk: 8192KB (Used:15%)
Touch screen to start test
4K-aligned R/W optimization
FatFS OK - System Ready
Cleaning existing files...
Cleaned: 12 files, 3 dirs
Flash cleaned - Ready for test
```

### 2. 主菜单导航
- 启动后自动显示主菜单
- 使用4个滑动手势选择测试项目
- 点击进入更多选项

### 3. 测试执行
- 每个测试执行后显示结果
- 自动返回主菜单
- 可重复执行测试

### 4. 高级功能
- 点击进入子菜单
- 执行系统管理功能
- 右滑退出程序

## 测试项目详解

### 磁盘信息测试 (上滑)
```
=== Disk Information Test ===
FAT Type: 3
Sector Size: 512 bytes
Cluster Size: 8 sectors
Total Space: 8192 KB
Free Space: 6963 KB
Usage: 15%
Disk info retrieved OK!
Any gesture to return
```

### 文件操作测试 (下滑)
```
=== File Operations Test ===
1. Create dir: OK
2. Write file: OK
3. Copy file: OK
4. Move file: OK
5. File exists: Yes
File operations test OK!
Any gesture to return
```

### 4K对齐读写测试 (左滑)
```
=== 4K Alignment R/W Test ===
1. 4K Write: OK (45ms)
2. 4K Read: OK (12ms)
   Bytes read: 1024
3. Data verify: PASS
4. 16K Write: OK (180ms)
4K alignment test OK!
Any gesture to return
```

### 批量操作测试 (右滑)
```
=== Batch Operations Test ===
1. Creating test files...
   Create files: OK
2. Batch list files:
batch_test_0.txt
batch_test_1.txt
3. Batch count: OK
4. Batch delete: OK
Operation OK, processed 5 files
Batch operations test OK!
Any gesture to return
```

### 性能测试 (子菜单上滑)
```
=== Performance Test ===
1. Sequential write test (10x4K)...
   Write time: 450 ms
   Write speed: 88 KB/s
2. Sequential read test (10x4K)...
   Read time: 120 ms
   Read speed: 83 KB/s
3. File copy test...
   Copy time: 67 ms
Performance test OK!
Any gesture to return
```

## 手势操作技巧

### ✅ **正确操作**
- 手势要清晰明确
- 滑动距离要足够
- 避免过快连续操作
- 等待测试完成后再操作

### ❌ **避免操作**
- 不要使用长按手势
- 不要使用多点触摸
- 不要在测试执行中操作
- 不要过于频繁触摸

## 系统特点

### 🎯 **简化操作**
- 仅使用5种基本手势
- 直观的菜单导航
- 清晰的功能分类

### 📱 **用户友好**
- 实时状态显示
- 英文界面
- 操作结果反馈

### ⚡ **高效测试**
- 4K对齐优化
- 批量操作支持
- 性能指标显示

### 🛠️ **系统管理**
- 磁盘格式化
- 临时文件清理
- 系统信息显示

这个5手势操作系统提供了完整的FatFS测试功能，操作简单直观，适合快速验证文件系统性能和功能。
