{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/bin/cmake.exe", "cpack": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/bin/cpack.exe", "ctest": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/bin/ctest.exe", "root": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31"}, "version": {"isDirty": false, "major": 3, "minor": 31, "patch": 8, "string": "3.31.8", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-fb8299cd484523693428.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-30ebf6c845804cdb0d9c.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-84c39ad3c01e966879c8.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-5943d6701ad4e4bba3a8.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-30ebf6c845804cdb0d9c.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-fb8299cd484523693428.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "toolchains-v1-5943d6701ad4e4bba3a8.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-84c39ad3c01e966879c8.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}