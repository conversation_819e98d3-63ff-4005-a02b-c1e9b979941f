{"artifacts": [{"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Core/Src/system_stm32f7xx.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_cortex.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma2d.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rcc.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rcc_ex.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_flash.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_flash_ex.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_gpio.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma_ex.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_pwr.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_pwr_ex.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_i2c.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_i2c_ex.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_exti.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_ll_fmc.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_nor.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_sram.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_nand.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_sdram.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_ltdc.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_ltdc_ex.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dsi.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_qspi.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rtc.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rtc_ex.c.obj"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_sources"], "files": ["cmake/stm32cubemx/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 98, "parent": 0}, {"command": 1, "file": 0, "line": 100, "parent": 0}, {"command": 2, "file": 0, "line": 99, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11"}], "defines": [{"backtrace": 2, "define": "DEBUG"}, {"backtrace": 2, "define": "STM32F767xx"}, {"backtrace": 2, "define": "USE_HAL_DRIVER"}], "includes": [{"backtrace": 2, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc"}, {"backtrace": 2, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target"}, {"backtrace": 2, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App"}, {"backtrace": 2, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc"}, {"backtrace": 2, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy"}, {"backtrace": 2, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src"}, {"backtrace": 2, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include"}, {"backtrace": 2, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include"}], "language": "C", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]}], "id": "STM32_Drivers::@768a070a0fe75716b479", "name": "STM32_Drivers", "paths": {"build": "cmake/stm32cubemx", "source": "cmake/stm32cubemx"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "Core/Src/system_stm32f7xx.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_cortex.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma2d.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rcc.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rcc_ex.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_flash.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_flash_ex.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_gpio.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma_ex.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_pwr.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_pwr_ex.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_i2c.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_i2c_ex.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_exti.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_ll_fmc.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_nor.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_sram.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_nand.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_sdram.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_ltdc.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_ltdc_ex.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dsi.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_qspi.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rtc.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rtc_ex.c", "sourceGroupIndex": 0}], "type": "OBJECT_LIBRARY"}