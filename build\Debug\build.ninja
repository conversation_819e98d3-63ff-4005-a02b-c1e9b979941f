# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.31

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: Fatfs
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug/
# =============================================================================
# Object build statements for EXECUTABLE target Fatfs


#############################################
# Order-only phony target for Fatfs

build cmake_object_order_depends_target_Fatfs: phony || cmake_object_order_depends_target_FatFs cmake_object_order_depends_target_STM32_Drivers

build CMakeFiles/Fatfs.dir/FATFS/App/fatfs.c.obj: C_COMPILER__Fatfs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App/fatfs.c || cmake_object_order_depends_target_Fatfs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\Fatfs.dir\FATFS\App\fatfs.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = CMakeFiles\Fatfs.dir
  OBJECT_FILE_DIR = CMakeFiles\Fatfs.dir\FATFS\App

build CMakeFiles/Fatfs.dir/FATFS/Target/user_diskio.c.obj: C_COMPILER__Fatfs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target/user_diskio.c || cmake_object_order_depends_target_Fatfs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\Fatfs.dir\FATFS\Target\user_diskio.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = CMakeFiles\Fatfs.dir
  OBJECT_FILE_DIR = CMakeFiles\Fatfs.dir\FATFS\Target

build CMakeFiles/Fatfs.dir/Core/Src/main.c.obj: C_COMPILER__Fatfs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Src/main.c || cmake_object_order_depends_target_Fatfs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\Fatfs.dir\Core\Src\main.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = CMakeFiles\Fatfs.dir
  OBJECT_FILE_DIR = CMakeFiles\Fatfs.dir\Core\Src

build CMakeFiles/Fatfs.dir/Core/Src/gpio.c.obj: C_COMPILER__Fatfs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Src/gpio.c || cmake_object_order_depends_target_Fatfs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\Fatfs.dir\Core\Src\gpio.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = CMakeFiles\Fatfs.dir
  OBJECT_FILE_DIR = CMakeFiles\Fatfs.dir\Core\Src

build CMakeFiles/Fatfs.dir/Core/Src/dma2d.c.obj: C_COMPILER__Fatfs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Src/dma2d.c || cmake_object_order_depends_target_Fatfs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\Fatfs.dir\Core\Src\dma2d.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = CMakeFiles\Fatfs.dir
  OBJECT_FILE_DIR = CMakeFiles\Fatfs.dir\Core\Src

build CMakeFiles/Fatfs.dir/Core/Src/fmc.c.obj: C_COMPILER__Fatfs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Src/fmc.c || cmake_object_order_depends_target_Fatfs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\Fatfs.dir\Core\Src\fmc.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = CMakeFiles\Fatfs.dir
  OBJECT_FILE_DIR = CMakeFiles\Fatfs.dir\Core\Src

build CMakeFiles/Fatfs.dir/Core/Src/i2c.c.obj: C_COMPILER__Fatfs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Src/i2c.c || cmake_object_order_depends_target_Fatfs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\Fatfs.dir\Core\Src\i2c.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = CMakeFiles\Fatfs.dir
  OBJECT_FILE_DIR = CMakeFiles\Fatfs.dir\Core\Src

build CMakeFiles/Fatfs.dir/Core/Src/ltdc.c.obj: C_COMPILER__Fatfs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Src/ltdc.c || cmake_object_order_depends_target_Fatfs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\Fatfs.dir\Core\Src\ltdc.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = CMakeFiles\Fatfs.dir
  OBJECT_FILE_DIR = CMakeFiles\Fatfs.dir\Core\Src

build CMakeFiles/Fatfs.dir/Core/Src/quadspi.c.obj: C_COMPILER__Fatfs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Src/quadspi.c || cmake_object_order_depends_target_Fatfs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\Fatfs.dir\Core\Src\quadspi.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = CMakeFiles\Fatfs.dir
  OBJECT_FILE_DIR = CMakeFiles\Fatfs.dir\Core\Src

build CMakeFiles/Fatfs.dir/Core/Src/rtc.c.obj: C_COMPILER__Fatfs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Src/rtc.c || cmake_object_order_depends_target_Fatfs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\Fatfs.dir\Core\Src\rtc.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = CMakeFiles\Fatfs.dir
  OBJECT_FILE_DIR = CMakeFiles\Fatfs.dir\Core\Src

build CMakeFiles/Fatfs.dir/Core/Src/stm32f7xx_it.c.obj: C_COMPILER__Fatfs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Src/stm32f7xx_it.c || cmake_object_order_depends_target_Fatfs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\Fatfs.dir\Core\Src\stm32f7xx_it.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = CMakeFiles\Fatfs.dir
  OBJECT_FILE_DIR = CMakeFiles\Fatfs.dir\Core\Src

build CMakeFiles/Fatfs.dir/Core/Src/stm32f7xx_hal_msp.c.obj: C_COMPILER__Fatfs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Src/stm32f7xx_hal_msp.c || cmake_object_order_depends_target_Fatfs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\Fatfs.dir\Core\Src\stm32f7xx_hal_msp.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = CMakeFiles\Fatfs.dir
  OBJECT_FILE_DIR = CMakeFiles\Fatfs.dir\Core\Src

build CMakeFiles/Fatfs.dir/Core/Src/sysmem.c.obj: C_COMPILER__Fatfs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Src/sysmem.c || cmake_object_order_depends_target_Fatfs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\Fatfs.dir\Core\Src\sysmem.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = CMakeFiles\Fatfs.dir
  OBJECT_FILE_DIR = CMakeFiles\Fatfs.dir\Core\Src

build CMakeFiles/Fatfs.dir/Core/Src/syscalls.c.obj: C_COMPILER__Fatfs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Src/syscalls.c || cmake_object_order_depends_target_Fatfs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\Fatfs.dir\Core\Src\syscalls.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = CMakeFiles\Fatfs.dir
  OBJECT_FILE_DIR = CMakeFiles\Fatfs.dir\Core\Src

build CMakeFiles/Fatfs.dir/startup_stm32f767xx.s.obj: ASM_COMPILER__Fatfs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/startup_stm32f767xx.s || cmake_object_order_depends_target_Fatfs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\Fatfs.dir\startup_stm32f767xx.s.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -x assembler-with-cpp -MMD -MP -g -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = CMakeFiles\Fatfs.dir
  OBJECT_FILE_DIR = CMakeFiles\Fatfs.dir

build CMakeFiles/Fatfs.dir/BSP/Src/W25QXX.c.obj: C_COMPILER__Fatfs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Src/W25QXX.c || cmake_object_order_depends_target_Fatfs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\Fatfs.dir\BSP\Src\W25QXX.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = CMakeFiles\Fatfs.dir
  OBJECT_FILE_DIR = CMakeFiles\Fatfs.dir\BSP\Src

build CMakeFiles/Fatfs.dir/BSP/Src/lcd.c.obj: C_COMPILER__Fatfs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Src/lcd.c || cmake_object_order_depends_target_Fatfs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\Fatfs.dir\BSP\Src\lcd.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = CMakeFiles\Fatfs.dir
  OBJECT_FILE_DIR = CMakeFiles\Fatfs.dir\BSP\Src

build CMakeFiles/Fatfs.dir/BSP/Src/lcd_test.c.obj: C_COMPILER__Fatfs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Src/lcd_test.c || cmake_object_order_depends_target_Fatfs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\Fatfs.dir\BSP\Src\lcd_test.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = CMakeFiles\Fatfs.dir
  OBJECT_FILE_DIR = CMakeFiles\Fatfs.dir\BSP\Src

build CMakeFiles/Fatfs.dir/BSP/Src/touch.c.obj: C_COMPILER__Fatfs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Src/touch.c || cmake_object_order_depends_target_Fatfs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\Fatfs.dir\BSP\Src\touch.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = CMakeFiles\Fatfs.dir
  OBJECT_FILE_DIR = CMakeFiles\Fatfs.dir\BSP\Src

build CMakeFiles/Fatfs.dir/BSP/Src/ff_opera_optimized.c.obj: C_COMPILER__Fatfs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Src/ff_opera_optimized.c || cmake_object_order_depends_target_Fatfs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\Fatfs.dir\BSP\Src\ff_opera_optimized.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = CMakeFiles\Fatfs.dir
  OBJECT_FILE_DIR = CMakeFiles\Fatfs.dir\BSP\Src


# =============================================================================
# Link build statements for EXECUTABLE target Fatfs


#############################################
# Link the executable Fatfs.elf

build Fatfs.elf: C_EXECUTABLE_LINKER__Fatfs_Debug cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Core/Src/system_stm32f7xx.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_cortex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma2d.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rcc.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rcc_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_flash.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_flash_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_gpio.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_pwr.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_pwr_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_i2c.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_i2c_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_exti.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_ll_fmc.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_nor.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_sram.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_nand.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_sdram.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_ltdc.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_ltdc_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dsi.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_qspi.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rtc.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rtc_ex.c.obj cmake/stm32cubemx/CMakeFiles/FatFs.dir/__/__/Middlewares/Third_Party/FatFs/src/diskio.c.obj cmake/stm32cubemx/CMakeFiles/FatFs.dir/__/__/Middlewares/Third_Party/FatFs/src/ff.c.obj cmake/stm32cubemx/CMakeFiles/FatFs.dir/__/__/Middlewares/Third_Party/FatFs/src/ff_gen_drv.c.obj cmake/stm32cubemx/CMakeFiles/FatFs.dir/__/__/Middlewares/Third_Party/FatFs/src/option/syscall.c.obj CMakeFiles/Fatfs.dir/FATFS/App/fatfs.c.obj CMakeFiles/Fatfs.dir/FATFS/Target/user_diskio.c.obj CMakeFiles/Fatfs.dir/Core/Src/main.c.obj CMakeFiles/Fatfs.dir/Core/Src/gpio.c.obj CMakeFiles/Fatfs.dir/Core/Src/dma2d.c.obj CMakeFiles/Fatfs.dir/Core/Src/fmc.c.obj CMakeFiles/Fatfs.dir/Core/Src/i2c.c.obj CMakeFiles/Fatfs.dir/Core/Src/ltdc.c.obj CMakeFiles/Fatfs.dir/Core/Src/quadspi.c.obj CMakeFiles/Fatfs.dir/Core/Src/rtc.c.obj CMakeFiles/Fatfs.dir/Core/Src/stm32f7xx_it.c.obj CMakeFiles/Fatfs.dir/Core/Src/stm32f7xx_hal_msp.c.obj CMakeFiles/Fatfs.dir/Core/Src/sysmem.c.obj CMakeFiles/Fatfs.dir/Core/Src/syscalls.c.obj CMakeFiles/Fatfs.dir/startup_stm32f767xx.s.obj CMakeFiles/Fatfs.dir/BSP/Src/W25QXX.c.obj CMakeFiles/Fatfs.dir/BSP/Src/lcd.c.obj CMakeFiles/Fatfs.dir/BSP/Src/lcd_test.c.obj CMakeFiles/Fatfs.dir/BSP/Src/touch.c.obj CMakeFiles/Fatfs.dir/BSP/Src/ff_opera_optimized.c.obj || cmake/stm32cubemx/FatFs cmake/stm32cubemx/STM32_Drivers
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3
  LINK_FLAGS = -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb
  OBJECT_DIR = CMakeFiles\Fatfs.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = Fatfs.elf
  TARGET_PDB = Fatfs.elf.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D F:\STM32cube\STM32-Clion\F767_HAL\DEMO6_Fatfs\build\Debug && F:\STM32cube\STM32cubeide\STM32CubeCLT_1.18.0\CMake\bin\cmake-gui.exe -SF:\STM32cube\STM32-Clion\F767_HAL\DEMO6_Fatfs -BF:\STM32cube\STM32-Clion\F767_HAL\DEMO6_Fatfs\build\Debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D F:\STM32cube\STM32-Clion\F767_HAL\DEMO6_Fatfs\build\Debug && F:\STM32cube\STM32cubeide\STM32CubeCLT_1.18.0\CMake\bin\cmake.exe --regenerate-during-build -SF:\STM32cube\STM32-Clion\F767_HAL\DEMO6_Fatfs -BF:\STM32cube\STM32-Clion\F767_HAL\DEMO6_Fatfs\build\Debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target STM32_Drivers


#############################################
# Order-only phony target for STM32_Drivers

build cmake_object_order_depends_target_STM32_Drivers: phony || .

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Core/Src/system_stm32f7xx.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Src/system_stm32f7xx.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Core\Src\system_stm32f7xx.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Core\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_cortex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_cortex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_cortex.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma2d.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma2d.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_dma2d.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rcc.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rcc.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_rcc.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rcc_ex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rcc_ex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_rcc_ex.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_flash.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_flash.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_flash.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_flash_ex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_flash_ex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_flash_ex.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_gpio.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_gpio.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_gpio.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_dma.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma_ex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma_ex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_dma_ex.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_pwr.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_pwr.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_pwr.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_pwr_ex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_pwr_ex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_pwr_ex.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_i2c.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_i2c.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_i2c.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_i2c_ex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_i2c_ex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_i2c_ex.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_exti.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_exti.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_exti.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_ll_fmc.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_ll_fmc.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_ll_fmc.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_nor.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_nor.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_nor.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_sram.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_sram.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_sram.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_nand.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_nand.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_nand.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_sdram.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_sdram.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_sdram.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_ltdc.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_ltdc.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_ltdc.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_ltdc_ex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_ltdc_ex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_ltdc_ex.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dsi.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dsi.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_dsi.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_qspi.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_qspi.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_qspi.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rtc.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rtc.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_rtc.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rtc_ex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rtc_ex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_rtc_ex.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32F7xx_HAL_Driver\Src



#############################################
# Object library STM32_Drivers

build cmake/stm32cubemx/STM32_Drivers: phony cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Core/Src/system_stm32f7xx.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_cortex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma2d.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rcc.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rcc_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_flash.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_flash_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_gpio.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_pwr.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_pwr_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_i2c.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_i2c_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_exti.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_ll_fmc.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_nor.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_sram.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_nand.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_sdram.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_ltdc.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_ltdc_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dsi.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_qspi.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rtc.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rtc_ex.c.obj

# =============================================================================
# Object build statements for OBJECT_LIBRARY target FatFs


#############################################
# Order-only phony target for FatFs

build cmake_object_order_depends_target_FatFs: phony || .

build cmake/stm32cubemx/CMakeFiles/FatFs.dir/__/__/Middlewares/Third_Party/FatFs/src/diskio.c.obj: C_COMPILER__FatFs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src/diskio.c || cmake_object_order_depends_target_FatFs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\FatFs.dir\__\__\Middlewares\Third_Party\FatFs\src\diskio.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\FatFs.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\FatFs.dir\__\__\Middlewares\Third_Party\FatFs\src

build cmake/stm32cubemx/CMakeFiles/FatFs.dir/__/__/Middlewares/Third_Party/FatFs/src/ff.c.obj: C_COMPILER__FatFs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src/ff.c || cmake_object_order_depends_target_FatFs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\FatFs.dir\__\__\Middlewares\Third_Party\FatFs\src\ff.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\FatFs.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\FatFs.dir\__\__\Middlewares\Third_Party\FatFs\src

build cmake/stm32cubemx/CMakeFiles/FatFs.dir/__/__/Middlewares/Third_Party/FatFs/src/ff_gen_drv.c.obj: C_COMPILER__FatFs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src/ff_gen_drv.c || cmake_object_order_depends_target_FatFs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\FatFs.dir\__\__\Middlewares\Third_Party\FatFs\src\ff_gen_drv.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\FatFs.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\FatFs.dir\__\__\Middlewares\Third_Party\FatFs\src

build cmake/stm32cubemx/CMakeFiles/FatFs.dir/__/__/Middlewares/Third_Party/FatFs/src/option/syscall.c.obj: C_COMPILER__FatFs_unscanned_Debug F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src/option/syscall.c || cmake_object_order_depends_target_FatFs
  DEFINES = -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\FatFs.dir\__\__\Middlewares\Third_Party\FatFs\src\option\syscall.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\FatFs.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\FatFs.dir\__\__\Middlewares\Third_Party\FatFs\src\option



#############################################
# Object library FatFs

build cmake/stm32cubemx/FatFs: phony cmake/stm32cubemx/CMakeFiles/FatFs.dir/__/__/Middlewares/Third_Party/FatFs/src/diskio.c.obj cmake/stm32cubemx/CMakeFiles/FatFs.dir/__/__/Middlewares/Third_Party/FatFs/src/ff.c.obj cmake/stm32cubemx/CMakeFiles/FatFs.dir/__/__/Middlewares/Third_Party/FatFs/src/ff_gen_drv.c.obj cmake/stm32cubemx/CMakeFiles/FatFs.dir/__/__/Middlewares/Third_Party/FatFs/src/option/syscall.c.obj


#############################################
# Utility command for edit_cache

build cmake/stm32cubemx/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D F:\STM32cube\STM32-Clion\F767_HAL\DEMO6_Fatfs\build\Debug\cmake\stm32cubemx && F:\STM32cube\STM32cubeide\STM32CubeCLT_1.18.0\CMake\bin\cmake-gui.exe -SF:\STM32cube\STM32-Clion\F767_HAL\DEMO6_Fatfs -BF:\STM32cube\STM32-Clion\F767_HAL\DEMO6_Fatfs\build\Debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build cmake/stm32cubemx/edit_cache: phony cmake/stm32cubemx/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build cmake/stm32cubemx/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D F:\STM32cube\STM32-Clion\F767_HAL\DEMO6_Fatfs\build\Debug\cmake\stm32cubemx && F:\STM32cube\STM32cubeide\STM32CubeCLT_1.18.0\CMake\bin\cmake.exe --regenerate-during-build -SF:\STM32cube\STM32-Clion\F767_HAL\DEMO6_Fatfs -BF:\STM32cube\STM32-Clion\F767_HAL\DEMO6_Fatfs\build\Debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build cmake/stm32cubemx/rebuild_cache: phony cmake/stm32cubemx/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build FatFs: phony cmake/stm32cubemx/FatFs

build Fatfs: phony Fatfs.elf

build STM32_Drivers: phony cmake/stm32cubemx/STM32_Drivers

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug

build all: phony Fatfs.elf cmake/stm32cubemx/all

# =============================================================================

#############################################
# Folder: F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug/cmake/stm32cubemx

build cmake/stm32cubemx/all: phony cmake/stm32cubemx/STM32_Drivers cmake/stm32cubemx/FatFs

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | CMakeCache.txt CMakeFiles/3.31.8/CMakeASMCompiler.cmake CMakeFiles/3.31.8/CMakeCCompiler.cmake CMakeFiles/3.31.8/CMakeCXXCompiler.cmake CMakeFiles/3.31.8/CMakeSystem.cmake F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/CMakeLists.txt F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/cmake/gcc-arm-none-eabi.cmake F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/cmake/stm32cubemx/CMakeLists.txt F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeASMInformation.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeCInformation.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeCXXInformation.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeGenericSystem.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeInitializeConfigs.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeSystemSpecificInformation.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeSystemSpecificInitialize.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Compiler/GNU-ASM.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Compiler/GNU-C.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Compiler/GNU-CXX.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Compiler/GNU.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Internal/CMakeASMLinkerInformation.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Internal/CMakeCLinkerInformation.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Internal/CMakeCXXLinkerInformation.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Platform/Generic.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles/3.31.8/CMakeASMCompiler.cmake CMakeFiles/3.31.8/CMakeCCompiler.cmake CMakeFiles/3.31.8/CMakeCXXCompiler.cmake CMakeFiles/3.31.8/CMakeSystem.cmake F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/CMakeLists.txt F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/cmake/gcc-arm-none-eabi.cmake F$:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/cmake/stm32cubemx/CMakeLists.txt F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeASMInformation.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeCInformation.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeCXXInformation.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeGenericSystem.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeInitializeConfigs.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeSystemSpecificInformation.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeSystemSpecificInitialize.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Compiler/GNU-ASM.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Compiler/GNU-C.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Compiler/GNU-CXX.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Compiler/GNU.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Internal/CMakeASMLinkerInformation.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Internal/CMakeCLinkerInformation.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Internal/CMakeCXXLinkerInformation.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake F$:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Platform/Generic.cmake: phony


#############################################
# Clean additional files.

build CMakeFiles/clean.additional: CLEAN_ADDITIONAL
  CONFIG = Debug


#############################################
# Clean all the built files.

build clean: CLEAN CMakeFiles/clean.additional


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
