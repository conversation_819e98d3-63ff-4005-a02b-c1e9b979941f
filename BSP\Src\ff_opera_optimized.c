//
// FatFS 优化操作函数库实现 - 4K对齐优化版本
// Created by wang on 2025/7/28.
//

#include "ff_opera_optimized.h"
#include "touch.h"

// 4K对齐缓冲区（静态分配，避免栈溢出）
static BYTE __attribute__((aligned(4096))) g_fat_buffer_4k[FAT_BUFFER_SIZE_4K];
static BYTE g_fat_buffer_1k[FAT_BUFFER_SIZE_1K];

/**
 * @brief 4K对齐写入文件
 * @param filename 文件名
 * @param data 数据指针
 * @param size 数据大小
 * @return FRESULT 操作结果
 */
FRESULT fat_WriteAligned(const TCHAR* filename, const void* data, UINT size) {
    FIL file;
    FRESULT res;
    UINT bytes_written;
    UINT total_written = 0;
    const BYTE* src_data = (const BYTE*)data;
    
    // 打开文件
    res = f_open(&file, filename, FA_CREATE_ALWAYS | FA_WRITE);
    if (res != FR_OK) {
        return res;
    }
    
    // 按4K块写入数据
    while (total_written < size) {
        UINT chunk_size = (size - total_written > FAT_BUFFER_SIZE_4K) ? 
                         FAT_BUFFER_SIZE_4K : (size - total_written);
        
        // 复制数据到4K对齐缓冲区
        memcpy(g_fat_buffer_4k, src_data + total_written, chunk_size);
        
        // 写入文件
        res = f_write(&file, g_fat_buffer_4k, chunk_size, &bytes_written);
        if (res != FR_OK || bytes_written != chunk_size) {
            f_close(&file);
            return (res != FR_OK) ? res : FR_DISK_ERR;
        }
        
        total_written += bytes_written;
    }
    
    // 同步并关闭文件
    f_sync(&file);
    f_close(&file);
    
    return FR_OK;
}

/**
 * @brief 4K对齐读取文件
 * @param filename 文件名
 * @param buffer 缓冲区指针
 * @param buffer_size 缓冲区大小
 * @param bytes_read 实际读取字节数
 * @return FRESULT 操作结果
 */
FRESULT fat_ReadAligned(const TCHAR* filename, void* buffer, UINT buffer_size, UINT* bytes_read) {
    FIL file;
    FRESULT res;
    UINT total_read = 0;
    BYTE* dst_buffer = (BYTE*)buffer;
    
    *bytes_read = 0;
    
    // 打开文件
    res = f_open(&file, filename, FA_READ);
    if (res != FR_OK) {
        return res;
    }
    
    // 按4K块读取数据
    while (total_read < buffer_size && !f_eof(&file)) {
        UINT chunk_size = (buffer_size - total_read > FAT_BUFFER_SIZE_4K) ? 
                         FAT_BUFFER_SIZE_4K : (buffer_size - total_read);
        UINT chunk_read;
        
        // 读取到4K对齐缓冲区
        res = f_read(&file, g_fat_buffer_4k, chunk_size, &chunk_read);
        if (res != FR_OK) {
            f_close(&file);
            return res;
        }
        
        if (chunk_read == 0) {
            break; // 文件结束
        }
        
        // 复制数据到目标缓冲区
        memcpy(dst_buffer + total_read, g_fat_buffer_4k, chunk_read);
        total_read += chunk_read;
    }
    
    f_close(&file);
    *bytes_read = total_read;
    
    return FR_OK;
}

/**
 * @brief 高效文件复制（4K缓冲区）
 * @param src_path 源文件路径
 * @param dst_path 目标文件路径
 * @return FRESULT 操作结果
 */
FRESULT fat_FastCopy(const TCHAR* src_path, const TCHAR* dst_path) {
    FIL src_file, dst_file;
    FRESULT res;
    UINT bytes_read, bytes_written;
    
    // 打开源文件
    res = f_open(&src_file, src_path, FA_READ);
    if (res != FR_OK) {
        return res;
    }
    
    // 创建目标文件
    res = f_open(&dst_file, dst_path, FA_CREATE_ALWAYS | FA_WRITE);
    if (res != FR_OK) {
        f_close(&src_file);
        return res;
    }
    
    // 使用4K缓冲区复制文件
    do {
        res = f_read(&src_file, g_fat_buffer_4k, FAT_BUFFER_SIZE_4K, &bytes_read);
        if (res != FR_OK) break;
        
        if (bytes_read > 0) {
            res = f_write(&dst_file, g_fat_buffer_4k, bytes_read, &bytes_written);
            if (res != FR_OK || bytes_written != bytes_read) {
                res = FR_DISK_ERR;
                break;
            }
        }
    } while (bytes_read == FAT_BUFFER_SIZE_4K);
    
    // 同步并关闭文件
    f_sync(&dst_file);
    f_close(&src_file);
    f_close(&dst_file);
    
    return res;
}

/**
 * @brief 快速移动/重命名文件
 * @param old_path 原路径
 * @param new_path 新路径
 * @return FRESULT 操作结果
 */
FRESULT fat_QuickMove(const TCHAR* old_path, const TCHAR* new_path) {
    return f_rename(old_path, new_path);
}

/**
 * @brief 创建完整路径（递归创建目录）
 * @param full_path 完整路径
 * @return FRESULT 操作结果
 */
FRESULT fat_CreatePath(const TCHAR* full_path) {
    char path_copy[FAT_MAX_PATH_LEN];
    char* token;
    char current_path[FAT_MAX_PATH_LEN] = "";
    FRESULT res = FR_OK;
    
    // 复制路径字符串
    strncpy(path_copy, full_path, FAT_MAX_PATH_LEN - 1);
    path_copy[FAT_MAX_PATH_LEN - 1] = '\0';
    
    // 分割路径并逐级创建目录
    token = strtok(path_copy, "/\\");
    while (token != NULL) {
        if (strlen(current_path) > 0) {
            strcat(current_path, "/");
        }
        strcat(current_path, token);
        
        // 尝试创建目录（如果已存在会返回FR_EXIST，这是正常的）
        res = f_mkdir(current_path);
        if (res != FR_OK && res != FR_EXIST) {
            return res;
        }
        
        token = strtok(NULL, "/\\");
    }
    
    return FR_OK;
}

/**
 * @brief 批量文件操作
 * @param pattern 文件匹配模式
 * @param dir_path 目录路径
 * @param operation_type 操作类型
 * @return FRESULT 操作结果
 */
FRESULT fat_BatchOperation(const TCHAR* pattern, const TCHAR* dir_path, UINT operation_type) {
    DIR dir;
    FILINFO file_info;
    FRESULT res;
    UINT count = 0;
    char full_path[FAT_MAX_PATH_LEN];
    const char* open_path;

    // Use root directory if dir_path is empty
    if (strlen(dir_path) == 0) {
        open_path = "";
    } else {
        open_path = dir_path;
    }

    // Open directory
    res = f_opendir(&dir, open_path);
    if (res != FR_OK) {
        return res;
    }

    // Traverse directory
    while (1) {
        res = f_readdir(&dir, &file_info);
        if (res != FR_OK || file_info.fname[0] == 0) {
            break;
        }

        // Check if matches pattern
        if (!(file_info.fattrib & AM_DIR) && strstr(file_info.fname, pattern) != NULL) {
            // For root directory, use filename directly
            if (strlen(dir_path) == 0) {
                strcpy(full_path, file_info.fname);
            } else {
                snprintf(full_path, FAT_MAX_PATH_LEN, "%s/%s", dir_path, file_info.fname);
            }

            switch (operation_type) {
                case FAT_OP_DELETE:
                    f_unlink(full_path);
                    break;
                case FAT_OP_COUNT:
                    // Count only, no operation
                    break;
                case FAT_OP_LIST:
                    // Display filename on LCD
                    LCD_ShowStringSimple(0, (count % 10) * 24 + 200, 24, file_info.fname);
                    break;
            }
            count++;
        }
    }
    
    f_closedir(&dir);
    
    // Display operation result
    char result_str[60];
    sprintf(result_str, "Operation OK, processed %d files", count);
    LCD_ShowStringSimple(0, 450, 24, result_str);
    
    return FR_OK;
}

/**
 * @brief 获取磁盘信息
 * @param disk_info 磁盘信息结构体指针
 * @return FRESULT 操作结果
 */
FRESULT fat_GetDiskInfo(fat_disk_info_t* disk_info) {
    FATFS *fs;
    DWORD free_clust;
    FRESULT res;
    
    // 获取剩余簇数
    res = f_getfree("0:", &free_clust, &fs);
    if (res != FR_OK) {
        return res;
    }
    
    // 计算空间信息
    DWORD total_sector = (fs->n_fatent - 2) * fs->csize;
    DWORD free_sector = free_clust * fs->csize;
    
#if _MAX_SS == _MIN_SS
    disk_info->free_space_kb = free_sector >> 1;    // 假设扇区大小为512字节
    disk_info->total_space_kb = total_sector >> 1;
    disk_info->sector_size = _MIN_SS;
#else
    disk_info->free_space_kb = (free_sector * fs->ssize) >> 10;
    disk_info->total_space_kb = (total_sector * fs->ssize) >> 10;
    disk_info->sector_size = fs->ssize;
#endif
    
    disk_info->used_space_kb = disk_info->total_space_kb - disk_info->free_space_kb;
    disk_info->usage_percent = (disk_info->used_space_kb * 100) / disk_info->total_space_kb;
    disk_info->fat_type = fs->fs_type;
    disk_info->cluster_size = fs->csize;
    
    return FR_OK;
}

/**
 * @brief 获取错误代码对应的中文描述
 * @param res FRESULT错误代码
 * @return 错误描述字符串
 */
const char* fat_GetErrorString(FRESULT res) {
    switch (res) {
        case FR_OK:                 return "OK";
        case FR_DISK_ERR:           return "Disk Error";
        case FR_INT_ERR:            return "Internal Error";
        case FR_NOT_READY:          return "Not Ready";
        case FR_NO_FILE:            return "No File";
        case FR_NO_PATH:            return "No Path";
        case FR_INVALID_NAME:       return "Invalid Name";
        case FR_DENIED:             return "Access Denied";
        case FR_EXIST:              return "File Exists";
        case FR_INVALID_OBJECT:     return "Invalid Object";
        case FR_WRITE_PROTECTED:    return "Write Protected";
        case FR_INVALID_DRIVE:      return "Invalid Drive";
        case FR_NOT_ENABLED:        return "Not Enabled";
        case FR_NO_FILESYSTEM:      return "No Filesystem";
        case FR_MKFS_ABORTED:       return "Format Aborted";
        case FR_TIMEOUT:            return "Timeout";
        case FR_LOCKED:             return "File Locked";
        case FR_NOT_ENOUGH_CORE:    return "Not Enough Memory";
        case FR_TOO_MANY_OPEN_FILES: return "Too Many Files";
        case FR_INVALID_PARAMETER:  return "Invalid Parameter";
        default:                    return "Unknown Error";
    }
}

/**
 * @brief 从RTC获取FAT时间戳
 * @return DWORD FAT时间戳
 */
DWORD fat_GetFatTimeFromRTC(void) {
    RTC_TimeTypeDef sTime;
    RTC_DateTypeDef sDate;
    if (HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN) == HAL_OK) {
        HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);
        WORD date = ((2000 + sDate.Year - 1980) << 9) | (sDate.Month << 5) | sDate.Date;
        WORD time = (sTime.Hours << 11) | (sTime.Minutes << 5) | (sTime.Seconds >> 1);

        DWORD fatTime = ((DWORD)date << 16) | time;
        return fatTime;
    } else {
        return 0;
    }
}

/**
 * @brief 检查文件是否存在
 * @param filename 文件名
 * @return FRESULT FR_OK表示存在，FR_NO_FILE表示不存在
 */
FRESULT fat_FileExists(const TCHAR* filename) {
    FILINFO file_info;
    return f_stat(filename, &file_info);
}

/**
 * @brief 获取文件信息
 * @param filename 文件名
 * @param file_info 文件信息结构体指针
 * @return FRESULT 操作结果
 */
FRESULT fat_GetFileInfo(const TCHAR* filename, fat_file_info_t* file_info) {
    FILINFO fno;
    FRESULT res = f_stat(filename, &fno);

    if (res == FR_OK) {
        strncpy(file_info->filename, fno.fname, 63);
        file_info->filename[63] = '\0';
        file_info->file_size = fno.fsize;
        file_info->file_date = fno.fdate;
        file_info->file_time = fno.ftime;
        file_info->file_attr = fno.fattrib;
    }

    return res;
}

// ================================
// 触摸菜单测试函数实现
// ================================

/**
 * @brief Display FatFS test main menu
 */
void fat_ShowMainMenu(void) {
    LCD_Clear(WHITE);
    LCD_ShowStringSimple(0, 0, 24, "=== FatFS 4K Optimized Test ===");
    LCD_ShowStringSimple(0, 48, 24, "[Swipe Up] Disk Info Test");
    LCD_ShowStringSimple(0, 72, 24, "[Swipe Down] File Operations");
    LCD_ShowStringSimple(0, 96, 24, "[Swipe Left] 4K Aligned R/W");
    LCD_ShowStringSimple(0, 120, 24, "[Swipe Right] Batch Operations");
    LCD_ShowStringSimple(0, 144, 24, "[Click] More Options...");
    LCD_ShowStringSimple(0, 200, 24, "Please select test item...");

    // Display system status
    LCD_ShowStringSimple(0, 250, 24, "System: FatFS 4K Optimized v1.0");
    fat_disk_info_t disk_info;
    if (fat_GetDiskInfo(&disk_info) == FR_OK) {
        char info_str[80];
        sprintf(info_str, "Disk Usage: %d%%", disk_info.usage_percent);
        LCD_ShowStringSimple(0, 274, 24, info_str);
    }
}

/**
 * @brief Display FatFS test sub menu
 */
void fat_ShowSubMenu(void) {
    LCD_Clear(WHITE);
    LCD_ShowStringSimple(0, 0, 24, "=== FatFS More Options ===");
    LCD_ShowStringSimple(0, 48, 24, "[Swipe Up] Performance Test");
    LCD_ShowStringSimple(0, 72, 24, "[Swipe Down] Format Disk");
    LCD_ShowStringSimple(0, 96, 24, "[Swipe Left] Clean All Files");
    LCD_ShowStringSimple(0, 120, 24, "[Swipe Right] Exit Test");
    LCD_ShowStringSimple(0, 144, 24, "[Click] Back to Main");
    LCD_ShowStringSimple(0, 200, 24, "Please select operation...");
}

/**
 * @brief Execute disk information test
 */
void fat_TestDiskInfo(void) {
    LCD_Clear(WHITE);
    LCD_ShowStringSimple(0, 0, 24, "=== Disk Information Test ===");

    fat_disk_info_t disk_info;
    FRESULT res = fat_GetDiskInfo(&disk_info);

    if (res == FR_OK) {
        char info_str[80];

        sprintf(info_str, "FAT Type: %d", disk_info.fat_type);
        LCD_ShowStringSimple(0, 48, 24, info_str);

        sprintf(info_str, "Sector Size: %d bytes", disk_info.sector_size);
        LCD_ShowStringSimple(0, 72, 24, info_str);

        sprintf(info_str, "Cluster Size: %d sectors", disk_info.cluster_size);
        LCD_ShowStringSimple(0, 96, 24, info_str);

        sprintf(info_str, "Total Space: %d KB", disk_info.total_space_kb);
        LCD_ShowStringSimple(0, 120, 24, info_str);

        sprintf(info_str, "Free Space: %d KB", disk_info.free_space_kb);
        LCD_ShowStringSimple(0, 144, 24, info_str);

        sprintf(info_str, "Usage: %d%%", disk_info.usage_percent);
        LCD_ShowStringSimple(0, 168, 24, info_str);

        LCD_ShowStringSimple(0, 220, 24, "Disk info retrieved OK!");
    } else {
        char error_str[80];
        sprintf(error_str, "Get disk info failed: %s", fat_GetErrorString(res));
        LCD_ShowStringSimple(0, 48, 24, error_str);
    }

    LCD_ShowStringSimple(0, 450, 24, "Any gesture to return");

    // Wait for user gesture to return
    Touch_WaitForInput(0);
}

/**
 * @brief Execute file operations test
 */
void fat_TestFileOperations(void) {
    LCD_Clear(WHITE);
    LCD_ShowStringSimple(0, 0, 24, "=== File Operations Test ===");

    FRESULT res;
    char status_str[80];

    // 1. Create test directory
    res = f_mkdir("test");
    sprintf(status_str, "1. Create dir: %s", fat_GetErrorString(res));
    LCD_ShowStringSimple(0, 48, 24, status_str);

    // 2. Write test file
    const char test_data[] = "4K-aligned write test data.\nFile operation test.";
    res = fat_WriteAligned("test_file.txt", test_data, strlen(test_data));
    sprintf(status_str, "2. Write file: %s", fat_GetErrorString(res));
    LCD_ShowStringSimple(0, 72, 24, status_str);

    // 3. Copy file
    res = fat_FastCopy("test_file.txt", "test_copy.txt");
    sprintf(status_str, "3. Copy file: %s", fat_GetErrorString(res));
    LCD_ShowStringSimple(0, 96, 24, status_str);

    // 4. Move file
    res = fat_QuickMove("test_copy.txt", "test/moved_file.txt");
    sprintf(status_str, "4. Move file: %s", fat_GetErrorString(res));
    LCD_ShowStringSimple(0, 120, 24, status_str);

    // 5. Check file exists
    res = fat_FileExists("test/moved_file.txt");
    sprintf(status_str, "5. File exists: %s", (res == FR_OK) ? "Yes" : "No");
    LCD_ShowStringSimple(0, 144, 24, status_str);

    LCD_ShowStringSimple(0, 200, 24, "File operations test OK!");
    LCD_ShowStringSimple(0, 450, 24, "Any gesture to return");

    // Wait for user gesture to return
    Touch_WaitForInput(0);
}

/**
 * @brief Execute 4K alignment read/write test
 */
void fat_Test4KAlignment(void) {
    LCD_Clear(WHITE);
    LCD_ShowStringSimple(0, 0, 24, "=== 4K Alignment R/W Test ===");

    FRESULT res;
    char status_str[80];
    UINT bytes_read;
    uint32_t start_time, end_time;

    // 创建4K测试数据
    for (int i = 0; i < FAT_BUFFER_SIZE_4K; i++) {
        g_fat_buffer_4k[i] = (BYTE)(i & 0xFF);
    }

    // 1. 4K aligned write test
    start_time = HAL_GetTick();
    res = fat_WriteAligned("test_4k.dat", g_fat_buffer_4k, FAT_BUFFER_SIZE_4K);
    end_time = HAL_GetTick();

    sprintf(status_str, "1. 4K Write: %s (%dms)", fat_GetErrorString(res), end_time - start_time);
    LCD_ShowStringSimple(0, 48, 24, status_str);

    // 2. 4K aligned read test
    memset(g_fat_buffer_1k, 0, FAT_BUFFER_SIZE_1K); // Clear 1K buffer for read
    start_time = HAL_GetTick();
    res = fat_ReadAligned("test_4k.dat", g_fat_buffer_1k, FAT_BUFFER_SIZE_1K, &bytes_read);
    end_time = HAL_GetTick();

    sprintf(status_str, "2. 4K Read: %s (%dms)", fat_GetErrorString(res), end_time - start_time);
    LCD_ShowStringSimple(0, 72, 24, status_str);

    sprintf(status_str, "   Bytes read: %d", bytes_read);
    LCD_ShowStringSimple(0, 96, 24, status_str);

    // 3. Data verification
    uint8_t data_valid = 1;
    for (int i = 0; i < FAT_BUFFER_SIZE_1K && i < bytes_read; i++) {
        if (g_fat_buffer_1k[i] != (BYTE)(i & 0xFF)) {
            data_valid = 0;
            break;
        }
    }

    sprintf(status_str, "3. Data verify: %s", data_valid ? "PASS" : "FAIL");
    LCD_ShowStringSimple(0, 120, 24, status_str);

    // 4. Large file test (16K)
    start_time = HAL_GetTick();
    for (int i = 0; i < 4; i++) {
        res = fat_WriteAligned("test_16k.dat", g_fat_buffer_4k, FAT_BUFFER_SIZE_4K);
        if (res != FR_OK) break;
    }
    end_time = HAL_GetTick();

    sprintf(status_str, "4. 16K Write: %s (%dms)", fat_GetErrorString(res), end_time - start_time);
    LCD_ShowStringSimple(0, 144, 24, status_str);

    LCD_ShowStringSimple(0, 200, 24, "4K alignment test OK!");
    LCD_ShowStringSimple(0, 450, 24, "Any gesture to return");

    // Wait for user gesture to return
    Touch_WaitForInput(0);
}

/**
 * @brief Execute batch operations test
 */
void fat_TestBatchOperations(void) {
    LCD_Clear(WHITE);
    LCD_ShowStringSimple(0, 0, 24, "=== Batch Operations Test ===");

    FRESULT res;
    char status_str[80];

    // 1. Create multiple test files
    LCD_ShowStringSimple(0, 48, 24, "1. Creating test files...");
    for (int i = 0; i < 5; i++) {
        char filename[50];
        sprintf(filename, "batch_test_%d.txt", i);

        char file_content[100];
        sprintf(file_content, "Batch test file %d\nContent test", i);

        res = fat_WriteAligned(filename, file_content, strlen(file_content));
        if (res != FR_OK) break;
    }

    sprintf(status_str, "   Create files: %s", fat_GetErrorString(res));
    LCD_ShowStringSimple(0, 72, 24, status_str);

    // 2. Batch list files
    LCD_ShowStringSimple(0, 96, 24, "2. Batch list files:");
    res = fat_BatchOperation("batch_test", "", FAT_OP_LIST);

    // 3. Batch count
    res = fat_BatchOperation("batch_test", "", FAT_OP_COUNT);
    sprintf(status_str, "3. Batch count: %s", fat_GetErrorString(res));
    LCD_ShowStringSimple(0, 350, 24, status_str);

    // 4. Batch delete
    res = fat_BatchOperation("batch_test", "", FAT_OP_DELETE);
    sprintf(status_str, "4. Batch delete: %s", fat_GetErrorString(res));
    LCD_ShowStringSimple(0, 374, 24, status_str);

    LCD_ShowStringSimple(0, 420, 24, "Batch operations test OK!");
    LCD_ShowStringSimple(0, 450, 24, "Any gesture to return");

    // Wait for user gesture to return
    Touch_WaitForInput(0);
}

/**
 * @brief Execute performance test
 */
void fat_TestPerformance(void) {
    LCD_Clear(WHITE);
    LCD_ShowStringSimple(0, 0, 24, "=== Performance Test ===");

    FRESULT res;
    char status_str[80];
    uint32_t start_time, end_time;
    UINT bytes_read;

    // Prepare test data
    for (int i = 0; i < FAT_BUFFER_SIZE_4K; i++) {
        g_fat_buffer_4k[i] = (BYTE)(i & 0xFF);
    }

    // 1. Sequential write performance test
    LCD_ShowStringSimple(0, 48, 24, "1. Sequential write test (10x4K)...");
    start_time = HAL_GetTick();

    for (int i = 0; i < 10; i++) {
        char filename[50];
        sprintf(filename, "perf_write_%d.dat", i);
        res = fat_WriteAligned(filename, g_fat_buffer_4k, FAT_BUFFER_SIZE_4K);
        if (res != FR_OK) break;
    }

    end_time = HAL_GetTick();
    uint32_t write_time = end_time - start_time;

    sprintf(status_str, "   Write time: %d ms", write_time);
    LCD_ShowStringSimple(0, 72, 24, status_str);

    sprintf(status_str, "   Write speed: %d KB/s", (40 * 1000) / write_time);
    LCD_ShowStringSimple(0, 96, 24, status_str);

    // 2. Sequential read performance test
    LCD_ShowStringSimple(0, 120, 24, "2. Sequential read test (10x4K)...");
    start_time = HAL_GetTick();

    for (int i = 0; i < 10; i++) {
        char filename[50];
        sprintf(filename, "perf_write_%d.dat", i);
        res = fat_ReadAligned(filename, g_fat_buffer_1k, FAT_BUFFER_SIZE_1K, &bytes_read);
        if (res != FR_OK) break;
    }

    end_time = HAL_GetTick();
    uint32_t read_time = end_time - start_time;

    sprintf(status_str, "   Read time: %d ms", read_time);
    LCD_ShowStringSimple(0, 144, 24, status_str);

    sprintf(status_str, "   Read speed: %d KB/s", (10 * 1000) / read_time);
    LCD_ShowStringSimple(0, 168, 24, status_str);

    // 3. File copy performance test
    LCD_ShowStringSimple(0, 192, 24, "3. File copy test...");
    start_time = HAL_GetTick();
    res = fat_FastCopy("perf_write_0.dat", "perf_copy.dat");
    end_time = HAL_GetTick();

    sprintf(status_str, "   Copy time: %d ms", end_time - start_time);
    LCD_ShowStringSimple(0, 216, 24, status_str);

    // Clean up test files
    fat_BatchOperation("perf_", "", FAT_OP_DELETE);

    LCD_ShowStringSimple(0, 270, 24, "Performance test OK!");
    LCD_ShowStringSimple(0, 450, 24, "Any gesture to return");

    // Wait for user gesture to return
    Touch_WaitForInput(0);
}

/**
 * @brief Run FatFS touch menu test
 */
void fat_RunTouchMenuTest(void) {
    touch_gesture_type_t gesture;
    uint8_t current_menu = 0; // 0=main menu, 1=sub menu
    uint8_t exit_test = 0;

    // Display main menu
    fat_ShowMainMenu();

    while (!exit_test) {
        // 等待触摸输入
        gesture = Touch_WaitForInput(0); // 无限等待

        if (current_menu == 0) {
            // Main menu handling
            switch (gesture) {
                case TOUCH_GESTURE_SWIPE_UP:
                    fat_TestDiskInfo();
                    fat_ShowMainMenu();
                    break;

                case TOUCH_GESTURE_SWIPE_DOWN:
                    fat_TestFileOperations();
                    fat_ShowMainMenu();
                    break;

                case TOUCH_GESTURE_SWIPE_LEFT:
                    fat_Test4KAlignment();
                    fat_ShowMainMenu();
                    break;

                case TOUCH_GESTURE_SWIPE_RIGHT:
                    fat_TestBatchOperations();
                    fat_ShowMainMenu();
                    break;

                case TOUCH_GESTURE_CLICK:
                    // Click to enter sub menu
                    current_menu = 1;
                    fat_ShowSubMenu();
                    break;

                default:
                    break;
            }
        } else {
            // Sub menu handling
            switch (gesture) {
                case TOUCH_GESTURE_SWIPE_UP:
                    // Performance test
                    fat_TestPerformance();
                    fat_ShowSubMenu();
                    break;

                case TOUCH_GESTURE_SWIPE_DOWN:
                    // Format disk
                    LCD_Clear(WHITE);
                    LCD_ShowStringSimple(0, 0, 24, "=== Format Disk ===");
                    LCD_ShowStringSimple(0, 48, 24, "Formatting...");

                    BYTE workBuffer[4096];
                    FRESULT res = f_mkfs("0:", FM_FAT32, 0, workBuffer, sizeof(workBuffer));

                    char result_str[60];
                    sprintf(result_str, "Format result: %s", fat_GetErrorString(res));
                    LCD_ShowStringSimple(0, 72, 24, result_str);

                    HAL_Delay(2000);
                    fat_ShowSubMenu();
                    break;

                case TOUCH_GESTURE_SWIPE_LEFT:
                    // Clean all files with confirmation
                    fat_CleanAllFilesWithConfirm();
                    fat_ShowSubMenu();
                    break;

                case TOUCH_GESTURE_SWIPE_RIGHT:
                    // Exit test
                    exit_test = 1;
                    break;

                case TOUCH_GESTURE_CLICK:
                    // Back to main menu
                    current_menu = 0;
                    fat_ShowMainMenu();
                    break;

                default:
                    break;
            }
        }

        HAL_Delay(100); // 防止触摸过于频繁
    }

    // Exit message
    LCD_Clear(WHITE);
    LCD_ShowStringSimple(0, 200, 24, "FatFS test exited");
    LCD_ShowStringSimple(0, 224, 24, "Thank you!");
}

// ================================
// System cleanup functions
// ================================

/**
 * @brief Recursively clean directory
 * Helper function to recursively delete all files and subdirectories
 */
static FRESULT fat_CleanDirectoryRecursive(const TCHAR* dir_path, UINT* deleted_files, UINT* deleted_dirs) {
    DIR dir;
    FILINFO file_info;
    FRESULT res;
    char full_path[FAT_MAX_PATH_LEN];

    res = f_opendir(&dir, dir_path);
    if (res != FR_OK) {
        return res;
    }

    // First pass: process all entries
    while (1) {
        res = f_readdir(&dir, &file_info);
        if (res != FR_OK || file_info.fname[0] == 0) {
            break;
        }

        // Skip system files and current/parent directory entries
        if (file_info.fname[0] == '.' ||
            strcmp(file_info.fname, ".") == 0 ||
            strcmp(file_info.fname, "..") == 0) {
            continue;
        }

        snprintf(full_path, FAT_MAX_PATH_LEN, "%s/%s", dir_path, file_info.fname);

        if (file_info.fattrib & AM_DIR) {
            // Recursively clean subdirectory
            fat_CleanDirectoryRecursive(full_path, deleted_files, deleted_dirs);
            // Then delete the empty directory
            res = f_unlink(full_path);
            if (res == FR_OK) {
                (*deleted_dirs)++;
            }
        } else {
            // Delete file
            res = f_unlink(full_path);
            if (res == FR_OK) {
                (*deleted_files)++;
            }
        }
    }

    f_closedir(&dir);
    return FR_OK;
}

/**
 * @brief Clean all files from flash before testing
 * Removes all existing files and directories to ensure clean test environment
 */
void fat_CleanAllFiles(void) {
    UINT deleted_files = 0;
    UINT deleted_dirs = 0;

    // Clean root directory recursively
    fat_CleanDirectoryRecursive("0:", &deleted_files, &deleted_dirs);

    // Display cleanup results
    char result_str[80];
    sprintf(result_str, "Cleaned: %d files, %d dirs", deleted_files, deleted_dirs);
    LCD_ShowStringSimple(0, 10 * 24, 24, result_str);
}

/**
 * @brief Advanced cleanup with user confirmation
 * Shows file count and asks for confirmation before cleanup
 */
FRESULT fat_CleanAllFilesWithConfirm(void) {
    DIR dir;
    FILINFO file_info;
    FRESULT res;
    UINT file_count = 0;
    UINT dir_count = 0;

    LCD_Clear(WHITE);
    LCD_ShowStringSimple(0, 0, 24, "=== Flash Cleanup ===");
    LCD_ShowStringSimple(0, 48, 24, "Scanning existing files...");

    // Count existing files and directories
    res = f_opendir(&dir, "0:/");
    if (res != FR_OK) {
        LCD_ShowStringSimple(0, 72, 24, "Error: Cannot open root dir");
        return res;
    }

    while (1) {
        res = f_readdir(&dir, &file_info);
        if (res != FR_OK || file_info.fname[0] == 0) {
            break;
        }

        // Skip system files
        if (file_info.fname[0] == '.') {
            continue;
        }

        if (file_info.fattrib & AM_DIR) {
            dir_count++;
        } else {
            file_count++;
        }
    }

    f_closedir(&dir);

    // Display file count
    char info_str[80];
    sprintf(info_str, "Found: %d files, %d directories", file_count, dir_count);
    LCD_ShowStringSimple(0, 72, 24, info_str);

    if (file_count == 0 && dir_count == 0) {
        LCD_ShowStringSimple(0, 96, 24, "Flash is already clean!");
        HAL_Delay(2000);
        return FR_OK;
    }

    // Ask for confirmation
    LCD_ShowStringSimple(0, 120, 24, "Clean all files?");
    LCD_ShowStringSimple(0, 144, 24, "[Swipe Up] Yes - Clean all");
    LCD_ShowStringSimple(0, 168, 24, "[Swipe Down] No - Keep files");

    // Wait for user input
    touch_gesture_type_t gesture = Touch_WaitForInput(10000); // 10 second timeout

    if (gesture == TOUCH_GESTURE_SWIPE_UP) {
        LCD_ShowStringSimple(0, 200, 24, "Cleaning files...");
        fat_CleanAllFiles();
        LCD_ShowStringSimple(0, 224, 24, "Cleanup completed!");
        HAL_Delay(2000);
        return FR_OK;
    } else {
        LCD_ShowStringSimple(0, 200, 24, "Cleanup cancelled");
        HAL_Delay(1000);
        return FR_DENIED;
    }
}
