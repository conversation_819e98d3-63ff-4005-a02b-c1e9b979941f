//
// FatFS 优化操作函数库 - 4K对齐优化版本
// Created by wang on 2025/7/28.
//

#ifndef FATFS_FF_OPERA_OPTIMIZED_H
#define FATFS_FF_OPERA_OPTIMIZED_H

#include "ff.h"
#include "lcd.h"
#include <stdio.h>
#include <string.h>
#include "rtc.h"

// 4K对齐缓冲区大小定义
#define FAT_BUFFER_SIZE_4K      4096    // 4K对齐缓冲区
#define FAT_BUFFER_SIZE_1K      1024    // 1K缓冲区
#define FAT_MAX_PATH_LEN        256     // 最大路径长度

// 操作类型定义
#define FAT_OP_DELETE           1       // 删除操作
#define FAT_OP_COUNT            2       // 计数操作
#define FAT_OP_LIST             3       // 列表操作

// 磁盘信息结构体
typedef struct {
    DWORD total_space_kb;       // 总空间(KB)
    DWORD free_space_kb;        // 剩余空间(KB)
    DWORD used_space_kb;        // 已用空间(KB)
    UINT  usage_percent;        // 使用率百分比
    BYTE  fat_type;             // FAT类型
    WORD  sector_size;          // 扇区大小
    DWORD cluster_size;         // 簇大小
} fat_disk_info_t;

// 文件信息结构体
typedef struct {
    char filename[64];          // 文件名
    DWORD file_size;           // 文件大小
    WORD file_date;            // 文件日期
    WORD file_time;            // 文件时间
    BYTE file_attr;            // 文件属性
} fat_file_info_t;

// ================================
// 核心文件操作函数 (4K对齐优化)
// ================================

/**
 * @brief 4K对齐写入文件
 * @param filename 文件名
 * @param data 数据指针
 * @param size 数据大小
 * @return FRESULT 操作结果
 */
FRESULT fat_WriteAligned(const TCHAR* filename, const void* data, UINT size);

/**
 * @brief 4K对齐读取文件
 * @param filename 文件名
 * @param buffer 缓冲区指针
 * @param buffer_size 缓冲区大小
 * @param bytes_read 实际读取字节数
 * @return FRESULT 操作结果
 */
FRESULT fat_ReadAligned(const TCHAR* filename, void* buffer, UINT buffer_size, UINT* bytes_read);

/**
 * @brief 高效文件复制（4K缓冲区）
 * @param src_path 源文件路径
 * @param dst_path 目标文件路径
 * @return FRESULT 操作结果
 */
FRESULT fat_FastCopy(const TCHAR* src_path, const TCHAR* dst_path);

// ================================
// 高效文件管理函数
// ================================

/**
 * @brief 快速移动/重命名文件
 * @param old_path 原路径
 * @param new_path 新路径
 * @return FRESULT 操作结果
 */
FRESULT fat_QuickMove(const TCHAR* old_path, const TCHAR* new_path);

/**
 * @brief 创建完整路径（递归创建目录）
 * @param full_path 完整路径
 * @return FRESULT 操作结果
 */
FRESULT fat_CreatePath(const TCHAR* full_path);

/**
 * @brief 批量文件操作
 * @param pattern 文件匹配模式
 * @param dir_path 目录路径
 * @param operation_type 操作类型
 * @return FRESULT 操作结果
 */
FRESULT fat_BatchOperation(const TCHAR* pattern, const TCHAR* dir_path, UINT operation_type);

// ================================
// 系统信息函数
// ================================

/**
 * @brief 获取磁盘信息
 * @param disk_info 磁盘信息结构体指针
 * @return FRESULT 操作结果
 */
FRESULT fat_GetDiskInfo(fat_disk_info_t* disk_info);

/**
 * @brief 获取错误代码对应的中文描述
 * @param res FRESULT错误代码
 * @return 错误描述字符串
 */
const char* fat_GetErrorString(FRESULT res);

// ================================
// 实用工具函数
// ================================

/**
 * @brief 从RTC获取FAT时间戳
 * @return DWORD FAT时间戳
 */
DWORD fat_GetFatTimeFromRTC(void);

/**
 * @brief 检查文件是否存在
 * @param filename 文件名
 * @return FRESULT FR_OK表示存在，FR_NO_FILE表示不存在
 */
FRESULT fat_FileExists(const TCHAR* filename);

/**
 * @brief 获取文件信息
 * @param filename 文件名
 * @param file_info 文件信息结构体指针
 * @return FRESULT 操作结果
 */
FRESULT fat_GetFileInfo(const TCHAR* filename, fat_file_info_t* file_info);

// ================================
// Touch menu test functions (5 gestures only)
// ================================

/**
 * @brief Display FatFS test main menu
 * Main menu: Up/Down/Left/Right for tests, Click for more options
 */
void fat_ShowMainMenu(void);

/**
 * @brief Display FatFS test sub menu
 * Sub menu: Up=Performance, Down=Format, Left=Clean, Right=Exit, Click=Back
 */
void fat_ShowSubMenu(void);

/**
 * @brief Run FatFS touch menu test
 * Uses only 5 gestures: Swipe Up/Down/Left/Right + Click
 */
void fat_RunTouchMenuTest(void);

/**
 * @brief Execute disk information test
 */
void fat_TestDiskInfo(void);

/**
 * @brief Execute file operations test
 */
void fat_TestFileOperations(void);

/**
 * @brief Execute 4K alignment read/write test
 */
void fat_Test4KAlignment(void);

/**
 * @brief Execute batch operations test
 */
void fat_TestBatchOperations(void);

/**
 * @brief Execute performance test
 */
void fat_TestPerformance(void);

// ================================
// System cleanup functions
// ================================

/**
 * @brief Clean all files from flash before testing
 * Removes all existing files and directories to ensure clean test environment
 */
void fat_CleanAllFiles(void);

/**
 * @brief Advanced cleanup with user confirmation
 * Shows file count and asks for confirmation before cleanup
 */
FRESULT fat_CleanAllFilesWithConfirm(void);

#endif //FATFS_FF_OPERA_OPTIMIZED_H
