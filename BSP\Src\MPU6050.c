#include "MPU6050.h"
#include "math.h"

#define ACCEL_SENS_2G      16384.0f
#define GYRO_SENS_2000DPS  16.4f
#define RAD_TO_DEG         57.2957795f
#define ALPHA              0.95328f
#define DT                 0.005f

static float ax_offset = 0, ay_offset = 0, az_offset = 0;
static float gx_offset = 0, gy_offset = 0, gz_offset = 0;

void MPU6050_Init(void) {
    uint8_t data;
    data = 0x80;
    HAL_I2C_Mem_Write(&hi2c1, MPU6050_ADDRESS, MPU6050_RA_PWR_MGMT_1, 1, &data, 1, 1000);
    HAL_Delay(100);
    data = 0x00;
    HAL_I2C_Mem_Write(&hi2c1, MPU6050_ADDRESS, MPU6050_RA_PWR_MGMT_1, 1, &data, 1, 1000);
    data = 0x07;
    HAL_I2C_Mem_Write(&hi2c1, MPU6050_ADDRESS, MPU6050_RA_SMPLRT_DIV, 1, &data, 1, 1000);
    data = MPU6050_DLPF_BW_42;
    HAL_I2C_Mem_Write(&hi2c1, MPU6050_ADDRESS, MPU6050_RA_CONFIG, 1, &data, 1, 1000);
    data = MPU6050_GYRO_FS_2000;
    HAL_I2C_Mem_Write(&hi2c1, MPU6050_ADDRESS, MPU6050_RA_GYRO_CONFIG, 1, &data, 1, 1000);
    data = MPU6050_ACCEL_FS_2G;
    HAL_I2C_Mem_Write(&hi2c1, MPU6050_ADDRESS, MPU6050_RA_ACCEL_CONFIG, 1, &data, 1, 1000);
    HAL_Delay(200);
}

int MPU6050_CalibrateOffsets(void) {
    float sum_ax = 0, sum_ay = 0, sum_az = 0;
    float sum_gx = 0, sum_gy = 0, sum_gz = 0;
    int success_count = 0;
    for (int i = 0; i < 100; i++) {
        float ax, ay, az, gx, gy, gz, temp;
        if (MPU6050_ReadRaw(&ax, &ay, &az, &temp, &gx, &gy, &gz) == 0) {
            sum_ax += ax;
            sum_ay += ay;
            sum_az += az;
            sum_gx += gx;
            sum_gy += gy;
            sum_gz += gz;
            success_count++;
        }
        HAL_Delay(20);
    }
    if (success_count < 10) return -1;
    ax_offset = sum_ax / success_count;
    ay_offset = sum_ay / success_count;
    az_offset = sum_az / success_count;
    gx_offset = sum_gx / success_count;
    gy_offset = sum_gy / success_count;
    gz_offset = sum_gz / success_count;
    return 0;
}

int MPU6050_ReadRaw(float *ax, float *ay, float *az, float *temperature, float *gx, float *gy, float *gz) {
    uint8_t data[14];
    if (HAL_I2C_Mem_Read(&hi2c1, MPU6050_ADDRESS, MPU6050_RA_ACCEL_XOUT_H, 1, data, 14, 1000) != HAL_OK) {
        return -1;
    }
    int16_t ax_raw = ((int16_t)data[0] << 8) | data[1];
    int16_t ay_raw = ((int16_t)data[2] << 8) | data[3];
    int16_t az_raw = ((int16_t)data[4] << 8) | data[5];
    int16_t temp_raw = ((int16_t)data[6] << 8) | data[7];
    int16_t gx_raw = ((int16_t)data[8] << 8) | data[9];
    int16_t gy_raw = ((int16_t)data[10] << 8) | data[11];
    int16_t gz_raw = ((int16_t)data[12] << 8) | data[13];
    *ax = ax_raw / ACCEL_SENS_2G;
    *ay = ay_raw / ACCEL_SENS_2G;
    *az = az_raw / ACCEL_SENS_2G;
    *temperature = temp_raw / 340.0f + 36.53f;
    *gx = gx_raw / GYRO_SENS_2000DPS;
    *gy = gy_raw / GYRO_SENS_2000DPS;
    *gz = gz_raw / GYRO_SENS_2000DPS;
    return 0;
}

int MPU6050_ReadData(float *ax, float *ay, float *az, float *temperature, float *gx, float *gy, float *gz, uint8_t keep_gravity) {
    float raw_ax, raw_ay, raw_az, raw_gx, raw_gy, raw_gz, temp;
    if (MPU6050_ReadRaw(&raw_ax, &raw_ay, &raw_az, &temp, &raw_gx, &raw_gy, &raw_gz) != 0)
        return -1;
    if (keep_gravity) {
        *ax = raw_ax;
        *ay = raw_ay;
        *az = raw_az;
    } else {
        *ax = raw_ax - ax_offset;
        *ay = raw_ay - ay_offset;
        *az = raw_az - az_offset;
    }
    *gx = raw_gx - gx_offset;
    *gy = raw_gy - gy_offset;
    *gz = raw_gz - gz_offset;
    *temperature = temp;
    return 0;
}

int MPU6050_Euler(float *yaw, float *pitch, float *roll) {
    static float y = 0.0f, p = 0.0f, r = 0.0f;
    static uint32_t last_time = 0;
    if (HAL_GetTick() > last_time) {
        float ax, ay, az, gx, gy, gz, temp;
        if (MPU6050_ReadData(&ax, &ay, &az, &temp, &gx, &gy, &gz, 1) == 0) {
            y += gz * DT;
            p += gx * DT;
            r -= gy * DT;
            *yaw = ALPHA * y;
            *pitch = ALPHA * p + (1 - ALPHA) * atan2f(ay, az) * RAD_TO_DEG;
            *roll = ALPHA * r + (1 - ALPHA) * atan2f(ax, az) * RAD_TO_DEG;
            last_time = HAL_GetTick() + 5;
            return 0;
        }
        return -1;
    }
    return -2;
}