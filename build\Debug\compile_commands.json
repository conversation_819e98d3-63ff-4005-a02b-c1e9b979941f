[{"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb -o CMakeFiles\\Fatfs.dir\\FATFS\\App\\fatfs.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\FATFS\\App\\fatfs.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\FATFS\\App\\fatfs.c", "output": "CMakeFiles\\Fatfs.dir\\FATFS\\App\\fatfs.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb -o CMakeFiles\\Fatfs.dir\\FATFS\\Target\\user_diskio.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\FATFS\\Target\\user_diskio.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\FATFS\\Target\\user_diskio.c", "output": "CMakeFiles\\Fatfs.dir\\FATFS\\Target\\user_diskio.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb -o CMakeFiles\\Fatfs.dir\\Core\\Src\\main.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\main.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\main.c", "output": "CMakeFiles\\Fatfs.dir\\Core\\Src\\main.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb -o CMakeFiles\\Fatfs.dir\\Core\\Src\\gpio.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\gpio.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\gpio.c", "output": "CMakeFiles\\Fatfs.dir\\Core\\Src\\gpio.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb -o CMakeFiles\\Fatfs.dir\\Core\\Src\\dma2d.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\dma2d.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\dma2d.c", "output": "CMakeFiles\\Fatfs.dir\\Core\\Src\\dma2d.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb -o CMakeFiles\\Fatfs.dir\\Core\\Src\\fmc.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\fmc.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\fmc.c", "output": "CMakeFiles\\Fatfs.dir\\Core\\Src\\fmc.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb -o CMakeFiles\\Fatfs.dir\\Core\\Src\\i2c.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\i2c.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\i2c.c", "output": "CMakeFiles\\Fatfs.dir\\Core\\Src\\i2c.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb -o CMakeFiles\\Fatfs.dir\\Core\\Src\\ltdc.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\ltdc.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\ltdc.c", "output": "CMakeFiles\\Fatfs.dir\\Core\\Src\\ltdc.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb -o CMakeFiles\\Fatfs.dir\\Core\\Src\\quadspi.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\quadspi.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\quadspi.c", "output": "CMakeFiles\\Fatfs.dir\\Core\\Src\\quadspi.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb -o CMakeFiles\\Fatfs.dir\\Core\\Src\\rtc.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\rtc.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\rtc.c", "output": "CMakeFiles\\Fatfs.dir\\Core\\Src\\rtc.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb -o CMakeFiles\\Fatfs.dir\\Core\\Src\\stm32f7xx_it.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\stm32f7xx_it.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\stm32f7xx_it.c", "output": "CMakeFiles\\Fatfs.dir\\Core\\Src\\stm32f7xx_it.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb -o CMakeFiles\\Fatfs.dir\\Core\\Src\\stm32f7xx_hal_msp.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\stm32f7xx_hal_msp.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\stm32f7xx_hal_msp.c", "output": "CMakeFiles\\Fatfs.dir\\Core\\Src\\stm32f7xx_hal_msp.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb -o CMakeFiles\\Fatfs.dir\\Core\\Src\\sysmem.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\sysmem.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\sysmem.c", "output": "CMakeFiles\\Fatfs.dir\\Core\\Src\\sysmem.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb -o CMakeFiles\\Fatfs.dir\\Core\\Src\\syscalls.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\syscalls.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\syscalls.c", "output": "CMakeFiles\\Fatfs.dir\\Core\\Src\\syscalls.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -x assembler-with-cpp -MMD -MP -g -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb -o CMakeFiles\\Fatfs.dir\\startup_stm32f767xx.s.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\startup_stm32f767xx.s", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\startup_stm32f767xx.s", "output": "CMakeFiles\\Fatfs.dir\\startup_stm32f767xx.s.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb -o CMakeFiles\\Fatfs.dir\\BSP\\Src\\W25QXX.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\BSP\\Src\\W25QXX.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\BSP\\Src\\W25QXX.c", "output": "CMakeFiles\\Fatfs.dir\\BSP\\Src\\W25QXX.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb -o CMakeFiles\\Fatfs.dir\\BSP\\Src\\lcd.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\BSP\\Src\\lcd.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\BSP\\Src\\lcd.c", "output": "CMakeFiles\\Fatfs.dir\\BSP\\Src\\lcd.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb -o CMakeFiles\\Fatfs.dir\\BSP\\Src\\lcd_test.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\BSP\\Src\\lcd_test.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\BSP\\Src\\lcd_test.c", "output": "CMakeFiles\\Fatfs.dir\\BSP\\Src\\lcd_test.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb -o CMakeFiles\\Fatfs.dir\\BSP\\Src\\touch.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\BSP\\Src\\touch.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\BSP\\Src\\touch.c", "output": "CMakeFiles\\Fatfs.dir\\BSP\\Src\\touch.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -mcpu=cortex-m7 -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mthumb -o CMakeFiles\\Fatfs.dir\\BSP\\Src\\ff_opera_optimized.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\BSP\\Src\\ff_opera_optimized.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\BSP\\Src\\ff_opera_optimized.c", "output": "CMakeFiles\\Fatfs.dir\\BSP\\Src\\ff_opera_optimized.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Core\\Src\\system_stm32f7xx.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\system_stm32f7xx.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Core\\Src\\system_stm32f7xx.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Core\\Src\\system_stm32f7xx.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_cortex.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_cortex.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_cortex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_cortex.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_dma2d.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_dma2d.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_dma2d.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_dma2d.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_rcc.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_rcc.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_rcc.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_rcc.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_rcc_ex.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_rcc_ex.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_rcc_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_rcc_ex.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_flash.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_flash.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_flash.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_flash.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_flash_ex.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_flash_ex.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_flash_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_flash_ex.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_gpio.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_gpio.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_gpio.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_gpio.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_dma.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_dma.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_dma.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_dma.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_dma_ex.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_dma_ex.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_dma_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_dma_ex.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_pwr.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_pwr.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_pwr.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_pwr.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_pwr_ex.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_pwr_ex.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_pwr_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_pwr_ex.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_i2c.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_i2c.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_i2c.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_i2c.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_i2c_ex.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_i2c_ex.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_i2c_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_i2c_ex.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_exti.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_exti.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_exti.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_exti.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_ll_fmc.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_ll_fmc.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_ll_fmc.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_ll_fmc.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_nor.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_nor.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_nor.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_nor.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_sram.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_sram.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_sram.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_sram.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_nand.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_nand.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_nand.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_nand.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_sdram.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_sdram.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_sdram.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_sdram.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_ltdc.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_ltdc.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_ltdc.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_ltdc.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_ltdc_ex.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_ltdc_ex.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_ltdc_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_ltdc_ex.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_dsi.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_dsi.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_dsi.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_dsi.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_qspi.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_qspi.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_qspi.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_qspi.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_rtc.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_rtc.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_rtc.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_rtc.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_rtc_ex.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_rtc_ex.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_rtc_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32F7xx_HAL_Driver\\Src\\stm32f7xx_hal_rtc_ex.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\FatFs.dir\\__\\__\\Middlewares\\Third_Party\\FatFs\\src\\diskio.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Middlewares\\Third_Party\\FatFs\\src\\diskio.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Middlewares\\Third_Party\\FatFs\\src\\diskio.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\FatFs.dir\\__\\__\\Middlewares\\Third_Party\\FatFs\\src\\diskio.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\FatFs.dir\\__\\__\\Middlewares\\Third_Party\\FatFs\\src\\ff.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Middlewares\\Third_Party\\FatFs\\src\\ff.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Middlewares\\Third_Party\\FatFs\\src\\ff.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\FatFs.dir\\__\\__\\Middlewares\\Third_Party\\FatFs\\src\\ff.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\FatFs.dir\\__\\__\\Middlewares\\Third_Party\\FatFs\\src\\ff_gen_drv.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Middlewares\\Third_Party\\FatFs\\src\\ff_gen_drv.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Middlewares\\Third_Party\\FatFs\\src\\ff_gen_drv.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\FatFs.dir\\__\\__\\Middlewares\\Third_Party\\FatFs\\src\\ff_gen_drv.c.obj"}, {"directory": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "command": "F:\\STM32cube\\STM32cubeide\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F767xx -DUSE_HAL_DRIVER -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include -IF:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\FatFs.dir\\__\\__\\Middlewares\\Third_Party\\FatFs\\src\\option\\syscall.c.obj -c F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Middlewares\\Third_Party\\FatFs\\src\\option\\syscall.c", "file": "F:\\STM32cube\\STM32-Clion\\F767_HAL\\DEMO6_Fatfs\\Middlewares\\Third_Party\\FatFs\\src\\option\\syscall.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\FatFs.dir\\__\\__\\Middlewares\\Third_Party\\FatFs\\src\\option\\syscall.c.obj"}]