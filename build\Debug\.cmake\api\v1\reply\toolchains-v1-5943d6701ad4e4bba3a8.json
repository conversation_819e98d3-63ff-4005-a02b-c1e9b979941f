{"kind": "toolchains", "toolchains": [{"compiler": {"id": "GNU", "implicit": {}, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc.exe", "version": ""}, "language": "ASM", "sourceFileExtensions": ["s", "S", "asm"]}, {"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/lib/gcc/arm-none-eabi/13.3.1/include", "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/lib/gcc/arm-none-eabi/13.3.1/include-fixed", "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/arm-none-eabi/include"], "linkDirectories": [], "linkFrameworkDirectories": [], "linkLibraries": []}, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc.exe", "version": "13.3.1"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/arm-none-eabi/include/c++/13.3.1", "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/arm-none-eabi/include/c++/13.3.1/arm-none-eabi/thumb/v7e-m+dp/hard", "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/arm-none-eabi/include/c++/13.3.1/backward", "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/lib/gcc/arm-none-eabi/13.3.1/include", "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/lib/gcc/arm-none-eabi/13.3.1/include-fixed", "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/arm-none-eabi/include"], "linkDirectories": [], "linkFrameworkDirectories": [], "linkLibraries": []}, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/arm-none-eabi-g++.exe", "version": "13.3.1"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "mm", "mpp", "CPP", "ixx", "cppm", "ccm", "cxxm", "c++m"]}], "version": {"major": 1, "minor": 0}}