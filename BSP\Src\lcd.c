/**
 ******************************************************************************
 * @file    lcd.c
 * <AUTHOR> LCD Driver Team
 * @brief   STM32F767 LCD驱动实现 - 支持单层/双层显示
 * @version V2.0.0
 * @date    2025-07-24
 ******************************************************************************
 * @attention
 *
 * 本驱动支持以下功能：
 * - 单层显示模式 (向后兼容)
 * - 双层显示模式 (Layer 0 + Layer 1)
 * - Alpha混合和透明度控制
 * - RGB565颜色格式
 * - DMA2D硬件加速
 * - PD7背光控制
 * - STM32CubeMX兼容
 *
 * 使用方法：
 * 1. 调用LCD_Init()初始化LCD
 * 2. 使用LCD_Layer_*函数进行双层操作
 * 3. 使用传统LCD_*函数保持向后兼容
 *
 ******************************************************************************
 */

#include "lcd.h"
#include "font.h"
#include "ltdc.h"
#include "string.h"

// ============================================================================
// 全局变量定义
// ============================================================================
uint32_t POINT_COLOR = 0xFF000000;
uint32_t BACK_COLOR  = 0xFFFFFFFF;

_ltdc_dev lcdltdc;
LTDC_HandleTypeDef LTDC_Handler;

// ============================================================================
// 内存分配 - 支持双层显示
// ============================================================================
#ifdef LCD_DUAL_LAYER_ENABLE
// 双层模式：为每层分配独立的帧缓冲区
// Layer 0 帧缓冲区 (背景层) - 建议大小：比显示分辨率稍大以提供缓冲
uint16_t ltdc_lcd_framebuf_layer0[900][550] __attribute__((section(".sdram_data")));
// Layer 1 帧缓冲区 (前景层) - 建议大小：比显示分辨率稍大以提供缓冲
uint16_t ltdc_lcd_framebuf_layer1[900][550] __attribute__((section(".sdram_data")));

// 帧缓冲区实际行宽定义（用于地址计算）
// 注意：这里使用显示宽度而不是帧缓冲区宽度，因为LTDC配置基于显示分辨率
#define FRAMEBUF_WIDTH  800  // 使用显示宽度进行地址计算

// 帧缓冲区指针数组
#ifdef LCD_DUAL_LAYER_ENABLE
uint32_t *ltdc_framebuf[LCD_MAX_LAYERS] = {
    (uint32_t *)ltdc_lcd_framebuf_layer0,   // Layer 0 - 指向数组首元素
    (uint32_t *)ltdc_lcd_framebuf_layer1    // Layer 1 - 指向数组首元素
};
#else
uint32_t *ltdc_framebuf[LCD_MAX_LAYERS] = {
    (uint32_t *)ltdc_lcd_framebuf0          // Layer 0 only - 指向数组首元素
};
#endif

// 帧缓冲区基地址定义 (用于HAL库配置)
#define LCD_LAYER0_FB_START_ADDR    ((uint32_t)ltdc_lcd_framebuf_layer0)
#define LCD_LAYER1_FB_START_ADDR    ((uint32_t)ltdc_lcd_framebuf_layer1)

#else
// 单层模式：保持原有内存分配格式以确保向后兼容
uint16_t ltdc_lcd_framebuf0[1024][640] __attribute__((section(".sdram_data")));
uint16_t ltdc_lcd_framebuf1[1024][640] __attribute__((section(".sdram_data")));

// 帧缓冲区实际行宽定义（用于地址计算）
#define FRAMEBUF_WIDTH  640  // 单层模式的帧缓冲区行宽

uint32_t *ltdc_framebuf[LCD_MAX_LAYERS] = {
    (uint32_t *)ltdc_lcd_framebuf0          // Layer 0 only - 指向数组首元素
};

#define LCD_LAYER0_FB_START_ADDR    ((uint32_t)ltdc_lcd_framebuf0)
#define LCD_LAYER1_FB_START_ADDR    ((uint32_t)ltdc_lcd_framebuf1)

#endif // LCD_DUAL_LAYER_ENABLE

// ============================================================================
// LTDC参数初始化函数 - 增强版支持双层
// ============================================================================
void LTDC_ParameterInit() {
    // 基础显示参数配置
    lcdltdc.pwidth  = 800;
    lcdltdc.pheight = 480;
    lcdltdc.width   = lcdltdc.pwidth;
    lcdltdc.height  = lcdltdc.pheight;
    lcdltdc.hsw     = 1;
    lcdltdc.vsw     = 3;
    lcdltdc.hbp     = 46;
    lcdltdc.vbp     = 23;
    lcdltdc.hfp     = 40;
    lcdltdc.vfp     = 13;
    lcdltdc.pixsize = 2;  // RGB565格式，每像素2字节
    lcdltdc.activelayer = 0;

    // 设置显示方向 (1=正常方向800x480, 0=旋转方向480x800)
    LTDC_Display_Dir(1);

#ifdef LCD_DUAL_LAYER_ENABLE
    // 双层模式初始化
    lcdltdc.dual_layer_enabled = 1;

    // 初始化Layer 0 (背景层)配置
    lcdltdc.layer_config[LCD_LAYER_0].layer_id = LCD_LAYER_0;
    lcdltdc.layer_config[LCD_LAYER_0].enabled = LCD_LAYER_ENABLED;
    lcdltdc.layer_config[LCD_LAYER_0].alpha = 255;  // 完全不透明
    lcdltdc.layer_config[LCD_LAYER_0].blend_mode = LCD_BLEND_MODE_NORMAL;
    lcdltdc.layer_config[LCD_LAYER_0].fb_start_addr = LCD_LAYER0_FB_START_ADDR;
    lcdltdc.layer_config[LCD_LAYER_0].window_x0 = 0;
    lcdltdc.layer_config[LCD_LAYER_0].window_y0 = 0;
    lcdltdc.layer_config[LCD_LAYER_0].window_x1 = 800;
    lcdltdc.layer_config[LCD_LAYER_0].window_y1 = 480;
    lcdltdc.layer_config[LCD_LAYER_0].default_color = WHITE;

    // 初始化Layer 1 (前景层)配置
    lcdltdc.layer_config[LCD_LAYER_1].layer_id = LCD_LAYER_1;
    lcdltdc.layer_config[LCD_LAYER_1].enabled = LCD_LAYER_DISABLED;  // 默认禁用
    lcdltdc.layer_config[LCD_LAYER_1].alpha = 128;  // 半透明
    lcdltdc.layer_config[LCD_LAYER_1].blend_mode = LCD_BLEND_MODE_ALPHA;
    lcdltdc.layer_config[LCD_LAYER_1].fb_start_addr = LCD_LAYER1_FB_START_ADDR;
    lcdltdc.layer_config[LCD_LAYER_1].window_x0 = 0;
    lcdltdc.layer_config[LCD_LAYER_1].window_y0 = 0;
    lcdltdc.layer_config[LCD_LAYER_1].window_x1 = 800;
    lcdltdc.layer_config[LCD_LAYER_1].window_y1 = 480;
    lcdltdc.layer_config[LCD_LAYER_1].default_color = 0x0000;  // 透明黑色

    // 双层模式下的层配置将由LCD_DualLayer_Init函数处理
    // 这里只进行基本的LTDC初始化

#else
    // 单层模式初始化 (保持向后兼容)
    HAL_LTDC_SetWindowPosition(&hltdc, 0, 0, 0);
    HAL_LTDC_SetWindowSize(&hltdc, 800, 480, 0);
#endif

    // 选择默认活动层并清屏
    LTDC_Select_Layer(0);
    LTDC_Clear(WHITE);

    // 保存LTDC句柄
    LTDC_Handler = hltdc;
}

// ============================================================================
// 错误处理和参数验证函数
// ============================================================================

#ifdef LCD_PARAM_CHECK_ENABLE

/**
 * @brief  验证层ID是否有效
 * @param  layer_id: 层ID
 * @retval uint8_t 1: 有效, 0: 无效
 */
static uint8_t LCD_IsValidLayer(uint8_t layer_id) {
#ifdef LCD_DUAL_LAYER_ENABLE
    return (layer_id < LCD_MAX_LAYERS);
#else
    return (layer_id == 0);
#endif
}

/**
 * @brief  验证坐标是否在有效范围内
 * @param  x: X坐标
 * @param  y: Y坐标
 * @retval uint8_t 1: 有效, 0: 无效
 */
static uint8_t LCD_IsValidCoordinate(uint16_t x, uint16_t y) {
    return (x < lcdltdc.width && y < lcdltdc.height);
}

/**
 * @brief  验证矩形区域是否有效
 * @param  sx: 起始X坐标
 * @param  sy: 起始Y坐标
 * @param  ex: 结束X坐标
 * @param  ey: 结束Y坐标
 * @retval uint8_t 1: 有效, 0: 无效
 */
static uint8_t LCD_IsValidRectangle(uint16_t sx, uint16_t sy, uint16_t ex, uint16_t ey) {
    return (sx < ex && sy < ey && ex <= lcdltdc.width && ey <= lcdltdc.height);
}

/**
 * @brief  验证字体大小是否支持
 * @param  size: 字体大小
 * @retval uint8_t 1: 支持, 0: 不支持
 */
static uint8_t LCD_IsValidFontSize(uint8_t size) {
    return (size == 12 || size == 16 || size == 24 || size == 32);
}

#endif // LCD_PARAM_CHECK_ENABLE

/**
 * @brief  错误处理函数
 * @param  error_code: 错误代码
 * @param  function_name: 发生错误的函数名
 * @retval None
 */
static void LCD_ErrorHandler(LCD_StatusTypeDef error_code, const char* function_name) {
#ifdef LCD_DEBUG_ENABLE
    switch (error_code) {
        case LCD_ERROR_INVALID_LAYER:
            LCD_ERROR_PRINT("无效的层ID - 函数: %s", function_name);
            break;
        case LCD_ERROR_INVALID_PARAM:
            LCD_ERROR_PRINT("无效的参数 - 函数: %s", function_name);
            break;
        case LCD_ERROR_MEMORY_ALLOC:
            LCD_ERROR_PRINT("内存分配失败 - 函数: %s", function_name);
            break;
        case LCD_ERROR_HAL_FAILED:
            LCD_ERROR_PRINT("HAL库调用失败 - 函数: %s", function_name);
            break;
        default:
            LCD_ERROR_PRINT("未知错误 - 函数: %s, 错误码: %d", function_name, error_code);
            break;
    }
#endif

    (void)error_code;      // 避免未使用变量警告
    (void)function_name;   // 避免未使用变量警告
}

// ============================================================================
// 基础LTDC控制函数
// ============================================================================
void LTDC_Display_Dir(uint8_t dir) {
    lcdltdc.dir = dir;
    if (dir == 0) {
        lcdltdc.width  = lcdltdc.pheight;
        lcdltdc.height = lcdltdc.pwidth;
    } else {
        lcdltdc.width  = lcdltdc.pwidth;
        lcdltdc.height = lcdltdc.pheight;
    }
}

void LTDC_Select_Layer(uint8_t layerx) {
#ifdef LCD_DUAL_LAYER_ENABLE
    if (layerx >= LCD_MAX_LAYERS) layerx = 0;
#else
    if (layerx > 1) layerx = 0;
#endif
    lcdltdc.activelayer = layerx;
}


// ============================================================================
// 双层显示核心管理函数实现
// ============================================================================
#ifdef LCD_DUAL_LAYER_ENABLE

/**
 * @brief  双层显示初始化
 * @retval LCD_StatusTypeDef 状态码
 */
LCD_StatusTypeDef LCD_DualLayer_Init(void) {
    LTDC_LayerCfgTypeDef pLayerCfg;

    // 配置Layer 0 (背景层)
    pLayerCfg.WindowX0 = lcdltdc.layer_config[LCD_LAYER_0].window_x0;
    pLayerCfg.WindowX1 = lcdltdc.layer_config[LCD_LAYER_0].window_x1;
    pLayerCfg.WindowY0 = lcdltdc.layer_config[LCD_LAYER_0].window_y0;
    pLayerCfg.WindowY1 = lcdltdc.layer_config[LCD_LAYER_0].window_y1;
    pLayerCfg.PixelFormat = LTDC_PIXEL_FORMAT_RGB565;
    pLayerCfg.Alpha = lcdltdc.layer_config[LCD_LAYER_0].alpha;
    pLayerCfg.Alpha0 = 0;
    pLayerCfg.BlendingFactor1 = LTDC_BLENDING_FACTOR1_CA;
    pLayerCfg.BlendingFactor2 = LTDC_BLENDING_FACTOR2_CA;
    pLayerCfg.FBStartAdress = lcdltdc.layer_config[LCD_LAYER_0].fb_start_addr;
    pLayerCfg.ImageWidth = lcdltdc.pwidth;
    pLayerCfg.ImageHeight = lcdltdc.pheight;
    pLayerCfg.Backcolor.Blue = (lcdltdc.layer_config[LCD_LAYER_0].default_color & 0x1F);
    pLayerCfg.Backcolor.Green = ((lcdltdc.layer_config[LCD_LAYER_0].default_color >> 5) & 0x3F);
    pLayerCfg.Backcolor.Red = ((lcdltdc.layer_config[LCD_LAYER_0].default_color >> 11) & 0x1F);

    if (HAL_LTDC_ConfigLayer(&hltdc, &pLayerCfg, LCD_LAYER_0) != HAL_OK) {
        return LCD_ERROR_HAL_FAILED;
    }

    // 配置Layer 1 (前景层)
    pLayerCfg.WindowX0 = lcdltdc.layer_config[LCD_LAYER_1].window_x0;
    pLayerCfg.WindowX1 = lcdltdc.layer_config[LCD_LAYER_1].window_x1;
    pLayerCfg.WindowY0 = lcdltdc.layer_config[LCD_LAYER_1].window_y0;
    pLayerCfg.WindowY1 = lcdltdc.layer_config[LCD_LAYER_1].window_y1;
    pLayerCfg.PixelFormat = LTDC_PIXEL_FORMAT_RGB565;
    pLayerCfg.Alpha = lcdltdc.layer_config[LCD_LAYER_1].alpha;
    pLayerCfg.Alpha0 = 0;
    pLayerCfg.BlendingFactor1 = LTDC_BLENDING_FACTOR1_PAxCA;
    pLayerCfg.BlendingFactor2 = LTDC_BLENDING_FACTOR2_PAxCA;
    pLayerCfg.FBStartAdress = lcdltdc.layer_config[LCD_LAYER_1].fb_start_addr;
    pLayerCfg.ImageWidth = lcdltdc.pwidth;
    pLayerCfg.ImageHeight = lcdltdc.pheight;
    pLayerCfg.Backcolor.Blue = (lcdltdc.layer_config[LCD_LAYER_1].default_color & 0x1F);
    pLayerCfg.Backcolor.Green = ((lcdltdc.layer_config[LCD_LAYER_1].default_color >> 5) & 0x3F);
    pLayerCfg.Backcolor.Red = ((lcdltdc.layer_config[LCD_LAYER_1].default_color >> 11) & 0x1F);

    if (HAL_LTDC_ConfigLayer(&hltdc, &pLayerCfg, LCD_LAYER_1) != HAL_OK) {
        return LCD_ERROR_HAL_FAILED;
    }

    // 根据配置启用或禁用层
    if (lcdltdc.layer_config[LCD_LAYER_0].enabled) {
        __HAL_LTDC_LAYER_ENABLE(&hltdc, LCD_LAYER_0);
    } else {
        __HAL_LTDC_LAYER_DISABLE(&hltdc, LCD_LAYER_0);
    }

    if (lcdltdc.layer_config[LCD_LAYER_1].enabled) {
        __HAL_LTDC_LAYER_ENABLE(&hltdc, LCD_LAYER_1);
    } else {
        __HAL_LTDC_LAYER_DISABLE(&hltdc, LCD_LAYER_1);
    }

    // 重新加载配置
    __HAL_LTDC_RELOAD_CONFIG(&hltdc);

    return LCD_OK;
}

/**
 * @brief  启用指定层
 * @param  layer_id: 层ID (0或1)
 * @retval LCD_StatusTypeDef 状态码
 */
LCD_StatusTypeDef LCD_Layer_Enable(uint8_t layer_id) {
    if (layer_id >= LCD_MAX_LAYERS) {
        return LCD_ERROR_INVALID_LAYER;
    }

    lcdltdc.layer_config[layer_id].enabled = LCD_LAYER_ENABLED;
    __HAL_LTDC_LAYER_ENABLE(&hltdc, layer_id);
    __HAL_LTDC_RELOAD_CONFIG(&hltdc);

    return LCD_OK;
}

/**
 * @brief  禁用指定层
 * @param  layer_id: 层ID (0或1)
 * @retval LCD_StatusTypeDef 状态码
 */
LCD_StatusTypeDef LCD_Layer_Disable(uint8_t layer_id) {
    if (layer_id >= LCD_MAX_LAYERS) {
        return LCD_ERROR_INVALID_LAYER;
    }

    lcdltdc.layer_config[layer_id].enabled = LCD_LAYER_DISABLED;
    __HAL_LTDC_LAYER_DISABLE(&hltdc, layer_id);
    __HAL_LTDC_RELOAD_CONFIG(&hltdc);

    return LCD_OK;
}

/**
 * @brief  设置层透明度
 * @param  layer_id: 层ID (0或1)
 * @param  alpha: 透明度值 (0-255)
 * @retval LCD_StatusTypeDef 状态码
 */
LCD_StatusTypeDef LCD_Layer_SetAlpha(uint8_t layer_id, uint8_t alpha) {
    if (layer_id >= LCD_MAX_LAYERS) {
        return LCD_ERROR_INVALID_LAYER;
    }

    lcdltdc.layer_config[layer_id].alpha = alpha;

    if (HAL_LTDC_SetAlpha(&hltdc, alpha, layer_id) != HAL_OK) {
        return LCD_ERROR_HAL_FAILED;
    }

    return LCD_OK;
}

/**
 * @brief  设置层混合模式
 * @param  layer_id: 层ID (0或1)
 * @param  blend_mode: 混合模式
 * @retval LCD_StatusTypeDef 状态码
 */
LCD_StatusTypeDef LCD_Layer_SetBlendMode(uint8_t layer_id, uint8_t blend_mode) {
    if (layer_id >= LCD_MAX_LAYERS) {
        return LCD_ERROR_INVALID_LAYER;
    }

    if (blend_mode > LCD_BLEND_MODE_MULTIPLY) {
        return LCD_ERROR_INVALID_PARAM;
    }

    lcdltdc.layer_config[layer_id].blend_mode = blend_mode;

    // 根据混合模式设置混合因子
    uint32_t blend_factor1, blend_factor2;
    switch (blend_mode) {
        case LCD_BLEND_MODE_NORMAL:
            blend_factor1 = LTDC_BLENDING_FACTOR1_CA;
            blend_factor2 = LTDC_BLENDING_FACTOR2_CA;
            break;
        case LCD_BLEND_MODE_ALPHA:
            blend_factor1 = LTDC_BLENDING_FACTOR1_PAxCA;
            blend_factor2 = LTDC_BLENDING_FACTOR2_PAxCA;
            break;
        case LCD_BLEND_MODE_MULTIPLY:
            blend_factor1 = LTDC_BLENDING_FACTOR1_PAxCA;
            blend_factor2 = LTDC_BLENDING_FACTOR2_PAxCA;
            break;
        default:
            return LCD_ERROR_INVALID_PARAM;
    }

    // 应用混合因子设置
    LTDC_LAYER(&hltdc, layer_id)->BFCR &= ~(LTDC_LxBFCR_BF2 | LTDC_LxBFCR_BF1);
    LTDC_LAYER(&hltdc, layer_id)->BFCR = (blend_factor1 | blend_factor2);

    __HAL_LTDC_RELOAD_CONFIG(&hltdc);

    return LCD_OK;
}

/**
 * @brief  设置层窗口位置和大小
 * @param  layer_id: 层ID (0或1)
 * @param  x0: 窗口起始X坐标
 * @param  y0: 窗口起始Y坐标
 * @param  x1: 窗口结束X坐标
 * @param  y1: 窗口结束Y坐标
 * @retval LCD_StatusTypeDef 状态码
 */
LCD_StatusTypeDef LCD_Layer_SetWindow(uint8_t layer_id, uint16_t x0, uint16_t y0, uint16_t x1, uint16_t y1) {
    if (layer_id >= LCD_MAX_LAYERS) {
        return LCD_ERROR_INVALID_LAYER;
    }

    if (x0 >= x1 || y0 >= y1 || x1 > lcdltdc.pwidth || y1 > lcdltdc.pheight) {
        return LCD_ERROR_INVALID_PARAM;
    }

    lcdltdc.layer_config[layer_id].window_x0 = x0;
    lcdltdc.layer_config[layer_id].window_y0 = y0;
    lcdltdc.layer_config[layer_id].window_x1 = x1;
    lcdltdc.layer_config[layer_id].window_y1 = y1;

    if (HAL_LTDC_SetWindowPosition(&hltdc, x0, y0, layer_id) != HAL_OK) {
        return LCD_ERROR_HAL_FAILED;
    }

    if (HAL_LTDC_SetWindowSize(&hltdc, x1 - x0, y1 - y0, layer_id) != HAL_OK) {
        return LCD_ERROR_HAL_FAILED;
    }

    return LCD_OK;
}

/**
 * @brief  设置层默认颜色
 * @param  layer_id: 层ID (0或1)
 * @param  color: 默认颜色 (RGB565格式)
 * @retval LCD_StatusTypeDef 状态码
 */
LCD_StatusTypeDef LCD_Layer_SetDefaultColor(uint8_t layer_id, uint32_t color) {
    if (layer_id >= LCD_MAX_LAYERS) {
        return LCD_ERROR_INVALID_LAYER;
    }

    lcdltdc.layer_config[layer_id].default_color = color;

    // 提取RGB分量 (RGB565 -> RGB888)
    uint8_t red = ((color >> 11) & 0x1F) << 3;
    uint8_t green = ((color >> 5) & 0x3F) << 2;
    uint8_t blue = (color & 0x1F) << 3;

    // 设置层默认颜色
    LTDC_LAYER(&hltdc, layer_id)->DCCR &= ~(LTDC_LxDCCR_DCBLUE | LTDC_LxDCCR_DCGREEN | LTDC_LxDCCR_DCRED);
    LTDC_LAYER(&hltdc, layer_id)->DCCR = ((uint32_t)red << 16) | ((uint32_t)green << 8) | blue;

    __HAL_LTDC_RELOAD_CONFIG(&hltdc);

    return LCD_OK;
}

/**
 * @brief  更新层配置
 * @param  layer_id: 层ID (0或1)
 * @retval LCD_StatusTypeDef 状态码
 */
LCD_StatusTypeDef LCD_Layer_ConfigUpdate(uint8_t layer_id) {
    if (layer_id >= LCD_MAX_LAYERS) {
        return LCD_ERROR_INVALID_LAYER;
    }

    __HAL_LTDC_RELOAD_CONFIG(&hltdc);

    return LCD_OK;
}

/**
 * @brief  检查层是否启用
 * @param  layer_id: 层ID (0或1)
 * @retval uint8_t 1: 启用, 0: 禁用
 */
uint8_t LCD_Layer_IsEnabled(uint8_t layer_id) {
    if (layer_id >= LCD_MAX_LAYERS) {
        return 0;
    }

    return lcdltdc.layer_config[layer_id].enabled;
}

/**
 * @brief  获取层透明度
 * @param  layer_id: 层ID (0或1)
 * @retval uint8_t 透明度值 (0-255)
 */
uint8_t LCD_Layer_GetAlpha(uint8_t layer_id) {
    if (layer_id >= LCD_MAX_LAYERS) {
        return 0;
    }

    return lcdltdc.layer_config[layer_id].alpha;
}

#endif // LCD_DUAL_LAYER_ENABLE

#ifdef LCD_DUAL_LAYER_ENABLE
// ============================================================================
// Alpha混合和层操作实用函数
// ============================================================================

/**
 * @brief  层间复制操作
 * @param  src_layer: 源层ID
 * @param  dst_layer: 目标层ID
 * @param  sx: 起始X坐标
 * @param  sy: 起始Y坐标
 * @param  ex: 结束X坐标
 * @param  ey: 结束Y坐标
 * @retval LCD_StatusTypeDef 状态码
 */
LCD_StatusTypeDef LCD_Layer_Copy(uint8_t src_layer, uint8_t dst_layer, uint16_t sx, uint16_t sy, uint16_t ex, uint16_t ey) {
    if (src_layer >= LCD_MAX_LAYERS || dst_layer >= LCD_MAX_LAYERS) {
        return LCD_ERROR_INVALID_LAYER;
    }

    if (sx >= ex || sy >= ey || ex > lcdltdc.pwidth || ey > lcdltdc.pheight) {
        return LCD_ERROR_INVALID_PARAM;
    }

    uint32_t src_addr = (uint32_t)ltdc_framebuf[src_layer] + lcdltdc.pixsize * (FRAMEBUF_WIDTH * sy + sx);
    uint32_t dst_addr = (uint32_t)ltdc_framebuf[dst_layer] + lcdltdc.pixsize * (FRAMEBUF_WIDTH * sy + sx);
    uint16_t width = ex - sx + 1;
    uint16_t height = ey - sy + 1;
    uint16_t offline = FRAMEBUF_WIDTH - width;

    // 使用DMA2D进行内存到内存复制
    RCC->AHB1ENR |= 1 << 23;  // 启用DMA2D时钟

    DMA2D->CR = 0 << 16;  // 内存到内存模式
    DMA2D->FGPFCCR = LTDC_PIXEL_FORMAT_RGB565;
    DMA2D->FGOR = offline;
    DMA2D->OPFCCR = LTDC_PIXEL_FORMAT_RGB565;
    DMA2D->OOR = offline;
    DMA2D->CR &= ~(1 << 0);
    DMA2D->FGMAR = src_addr;
    DMA2D->OMAR = dst_addr;
    DMA2D->NLR = height | (width << 16);
    DMA2D->CR |= 1 << 0;

    uint32_t timeout = 0;
    while ((DMA2D->ISR & (1 << 1)) == 0) {
        timeout++;
        if (timeout > 0x1FFFFF) break;
    }
    DMA2D->IFCR |= 1 << 1;

    return LCD_OK;
}

/**
 * @brief  两层混合操作
 * @param  layer1: 第一层ID
 * @param  layer2: 第二层ID
 * @param  blend_ratio: 混合比例 (0-255)
 * @retval LCD_StatusTypeDef 状态码
 */
LCD_StatusTypeDef LCD_Layer_Blend(uint8_t layer1, uint8_t layer2, uint8_t blend_ratio) {
    if (layer1 >= LCD_MAX_LAYERS || layer2 >= LCD_MAX_LAYERS) {
        return LCD_ERROR_INVALID_LAYER;
    }

    // 使用DMA2D进行Alpha混合
    uint32_t layer1_addr = (uint32_t)ltdc_framebuf[layer1];
    uint32_t layer2_addr = (uint32_t)ltdc_framebuf[layer2];

    RCC->AHB1ENR |= 1 << 23;  // 启用DMA2D时钟

    DMA2D->CR = 2 << 16;  // 内存到内存混合模式
    DMA2D->FGPFCCR = LTDC_PIXEL_FORMAT_RGB565 | (blend_ratio << 24);  // 前景层Alpha
    DMA2D->BGPFCCR = LTDC_PIXEL_FORMAT_RGB565 | ((255 - blend_ratio) << 24);  // 背景层Alpha
    DMA2D->FGOR = 0;
    DMA2D->BGOR = 0;
    DMA2D->OPFCCR = LTDC_PIXEL_FORMAT_RGB565;
    DMA2D->OOR = 0;
    DMA2D->CR &= ~(1 << 0);
    DMA2D->FGMAR = layer1_addr;
    DMA2D->BGMAR = layer2_addr;
    DMA2D->OMAR = layer1_addr;  // 结果写回layer1
    DMA2D->NLR = lcdltdc.pheight | (lcdltdc.pwidth << 16);
    DMA2D->CR |= 1 << 0;

    uint32_t timeout = 0;
    while ((DMA2D->ISR & (1 << 1)) == 0) {
        timeout++;
        if (timeout > 0x1FFFFF) break;
    }
    DMA2D->IFCR |= 1 << 1;

    return LCD_OK;
}

#endif // LCD_DUAL_LAYER_ENABLE

// ============================================================================
// 基础LTDC绘图函数 (保持向后兼容)
// ============================================================================
void LTDC_Draw_Point(uint16_t x, uint16_t y, uint32_t color) {
    if (lcdltdc.dir)
        *(uint16_t *)((uint32_t)ltdc_framebuf[lcdltdc.activelayer] + lcdltdc.pixsize * (FRAMEBUF_WIDTH * y + x)) = color;
    else
        *(uint16_t *)((uint32_t)ltdc_framebuf[lcdltdc.activelayer] + lcdltdc.pixsize * (FRAMEBUF_WIDTH * (lcdltdc.pheight - x - 1) + y)) = color;
}

uint32_t LTDC_Read_Point(uint16_t x, uint16_t y) {
    if (lcdltdc.dir)
        return *(uint16_t *)((uint32_t)ltdc_framebuf[lcdltdc.activelayer] + lcdltdc.pixsize * (FRAMEBUF_WIDTH * y + x));
    else
        return *(uint16_t *)((uint32_t)ltdc_framebuf[lcdltdc.activelayer] + lcdltdc.pixsize * (FRAMEBUF_WIDTH * (lcdltdc.pheight - x - 1) + y));
}

void LTDC_Fill(uint16_t sx, uint16_t sy, uint16_t ex, uint16_t ey, uint32_t color) {
    uint32_t psx, psy, pex, pey;
    uint32_t timeout = 0;
    uint16_t offline;
    uint32_t addr;
    if (lcdltdc.dir) {
        psx = sx; psy = sy; pex = ex; pey = ey;
    } else {
        psx = sy; psy = lcdltdc.pheight - ex - 1;
        pex = ey; pey = lcdltdc.pheight - sx - 1;
    }
    offline = FRAMEBUF_WIDTH - (pex - psx + 1);
    addr = ((uint32_t)ltdc_framebuf[lcdltdc.activelayer] + lcdltdc.pixsize * (FRAMEBUF_WIDTH * psy + psx));
    RCC->AHB1ENR |= 1 << 23;
    DMA2D->CR = 3 << 16;
    DMA2D->OPFCCR = LTDC_PIXEL_FORMAT_RGB565;
    DMA2D->OOR = offline;
    DMA2D->CR &= ~(1 << 0);
    DMA2D->OMAR = addr;
    DMA2D->NLR = (pey - psy + 1) | ((pex - psx + 1) << 16);
    DMA2D->OCOLR = color;
    DMA2D->CR |= 1 << 0;
    while ((DMA2D->ISR & (1 << 1)) == 0) {
        timeout++;
        if (timeout > 0x1FFFFF) break;
    }
    DMA2D->IFCR |= 1 << 1;
}

void LTDC_Color_Fill(uint16_t sx, uint16_t sy, uint16_t ex, uint16_t ey, uint16_t *color) {
    uint32_t psx, psy, pex, pey;
    uint32_t timeout = 0;
    uint16_t offline;
    uint32_t addr;
    if (lcdltdc.dir) {
        psx = sx; psy = sy; pex = ex; pey = ey;
    } else {
        psx = sy; psy = lcdltdc.pheight - ex - 1;
        pex = ey; pey = lcdltdc.pheight - sx - 1;
    }
    offline = FRAMEBUF_WIDTH - (pex - psx + 1);
    addr = ((uint32_t)ltdc_framebuf[lcdltdc.activelayer] + lcdltdc.pixsize * (FRAMEBUF_WIDTH * psy + psx));
    RCC->AHB1ENR |= 1 << 23;
    DMA2D->CR = 0 << 16;
    DMA2D->FGPFCCR = LCD_PIXFORMAT;
    DMA2D->FGOR = 0;
    DMA2D->OOR = offline;
    DMA2D->CR &= ~(1 << 0);
    DMA2D->FGMAR = (uint32_t)color;
    DMA2D->OMAR = addr;
    DMA2D->NLR = (pey - psy + 1) | ((pex - psx + 1) << 16);
    DMA2D->CR |= 1 << 0;
    while ((DMA2D->ISR & (1 << 1)) == 0) {
        timeout++;
        if (timeout > 0x1FFFFF) break;
    }
    DMA2D->IFCR |= 1 << 1;
}

void LTDC_Clear(uint32_t color) {
    LTDC_Fill(0, 0, lcdltdc.width - 1, lcdltdc.height - 1, color);
}

/**
 * @brief  LCD初始化函数 - 支持条件编译
 * @note   根据LCD_DUAL_LAYER_ENABLE宏决定初始化单层还是双层模式
 * @retval None
 */
void LCD_Init(void) {
    // 背光控制 - PD7引脚设置为高电平启用背光
    HAL_GPIO_WritePin(GPIOD, GPIO_PIN_7, GPIO_PIN_SET);

#ifdef LCD_DUAL_LAYER_ENABLE
    // 双层模式初始化
    LCD_StatusTypeDef result = LCD_DualLayer_Init();
    if (result == LCD_OK) {
        // 双层初始化成功，设置默认活动层为Layer 0
        LTDC_Select_Layer(LCD_LAYER_0);
        // 清除Layer 0 (背景层)
        LCD_Layer_Clear(LCD_LAYER_0, WHITE);
        // 清除Layer 1 (前景层) - 使用透明色
        LCD_Layer_Clear(LCD_LAYER_1, 0x0000);
    } else {
        // 双层初始化失败，回退到单层模式
        LTDC_Select_Layer(0);
        LTDC_Clear(WHITE);
    }
#else
    // 单层模式初始化 (保持向后兼容)
    LTDC_Select_Layer(0);
    LTDC_Clear(WHITE);
#endif
}

void LTDC_Switch(uint8_t sw) {
    if (sw == 1)
        __HAL_LTDC_ENABLE(&LTDC_Handler);
    else if (sw == 0)
        __HAL_LTDC_DISABLE(&LTDC_Handler);
}

#ifdef LCD_DUAL_LAYER_ENABLE
// ============================================================================
// 双层显示绘图函数实现
// ============================================================================

/**
 * @brief  清除指定层
 * @param  layer_id: 层ID (0或1)
 * @param  color: 清除颜色
 * @retval LCD_StatusTypeDef 状态码
 */
LCD_StatusTypeDef LCD_Layer_Clear(uint8_t layer_id, uint32_t color) {
    if (layer_id >= LCD_MAX_LAYERS) {
        return LCD_ERROR_INVALID_LAYER;
    }

    uint8_t old_layer = lcdltdc.activelayer;
    LTDC_Select_Layer(layer_id);
    LTDC_Clear(color);
    LTDC_Select_Layer(old_layer);

    return LCD_OK;
}

/**
 * @brief  在指定层填充矩形区域
 * @param  layer_id: 层ID (0或1)
 * @param  sx: 起始X坐标
 * @param  sy: 起始Y坐标
 * @param  ex: 结束X坐标
 * @param  ey: 结束Y坐标
 * @param  color: 填充颜色
 * @retval LCD_StatusTypeDef 状态码
 */
LCD_StatusTypeDef LCD_Layer_Fill(uint8_t layer_id, uint16_t sx, uint16_t sy, uint16_t ex, uint16_t ey, uint32_t color) {
    if (layer_id >= LCD_MAX_LAYERS) {
        return LCD_ERROR_INVALID_LAYER;
    }

    if (sx >= ex || sy >= ey || ex > lcdltdc.width || ey > lcdltdc.height) {
        return LCD_ERROR_INVALID_PARAM;
    }

    uint8_t old_layer = lcdltdc.activelayer;
    LTDC_Select_Layer(layer_id);
    LTDC_Fill(sx, sy, ex, ey, color);
    LTDC_Select_Layer(old_layer);

    return LCD_OK;
}

/**
 * @brief  在指定层绘制点
 * @param  layer_id: 层ID (0或1)
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  color: 点颜色
 * @retval LCD_StatusTypeDef 状态码
 */
LCD_StatusTypeDef LCD_Layer_DrawPoint(uint8_t layer_id, uint16_t x, uint16_t y, uint32_t color) {
    if (layer_id >= LCD_MAX_LAYERS) {
        return LCD_ERROR_INVALID_LAYER;
    }

    if (x >= lcdltdc.width || y >= lcdltdc.height) {
        return LCD_ERROR_INVALID_PARAM;
    }

    uint8_t old_layer = lcdltdc.activelayer;
    LTDC_Select_Layer(layer_id);
    LTDC_Draw_Point(x, y, color);
    LTDC_Select_Layer(old_layer);

    return LCD_OK;
}

/**
 * @brief  在指定层绘制直线
 * @param  layer_id: 层ID (0或1)
 * @param  x1: 起始X坐标
 * @param  y1: 起始Y坐标
 * @param  x2: 结束X坐标
 * @param  y2: 结束Y坐标
 * @param  color: 线条颜色
 * @retval LCD_StatusTypeDef 状态码
 */
LCD_StatusTypeDef LCD_Layer_DrawLine(uint8_t layer_id, uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint32_t color) {
    if (layer_id >= LCD_MAX_LAYERS) {
        return LCD_ERROR_INVALID_LAYER;
    }

    uint8_t old_layer = lcdltdc.activelayer;
    uint32_t old_point_color = POINT_COLOR;

    LTDC_Select_Layer(layer_id);
    POINT_COLOR = color;

    // 使用Bresenham算法绘制直线
    uint16_t t;
    int xerr = 0, yerr = 0, delta_x, delta_y, distance;
    int incx, incy, uRow, uCol;
    delta_x = x2 - x1;
    delta_y = y2 - y1;
    uRow = x1;
    uCol = y1;
    incx = (delta_x > 0) ? 1 : ((delta_x == 0) ? 0 : -1);
    if (delta_x < 0) delta_x = -delta_x;
    incy = (delta_y > 0) ? 1 : ((delta_y == 0) ? 0 : -1);
    if (delta_y < 0) delta_y = -delta_y;
    distance = (delta_x > delta_y) ? delta_x : delta_y;
    for (t = 0; t <= distance + 1; t++) {
        LTDC_Draw_Point(uRow, uCol, color);
        xerr += delta_x;
        yerr += delta_y;
        if (xerr > distance) { xerr -= distance; uRow += incx; }
        if (yerr > distance) { yerr -= distance; uCol += incy; }
    }

    POINT_COLOR = old_point_color;
    LTDC_Select_Layer(old_layer);

    return LCD_OK;
}

/**
 * @brief  在指定层绘制矩形
 * @param  layer_id: 层ID (0或1)
 * @param  x1: 左上角X坐标
 * @param  y1: 左上角Y坐标
 * @param  x2: 右下角X坐标
 * @param  y2: 右下角Y坐标
 * @param  color: 矩形颜色
 * @retval LCD_StatusTypeDef 状态码
 */
LCD_StatusTypeDef LCD_Layer_DrawRectangle(uint8_t layer_id, uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint32_t color) {
    if (layer_id >= LCD_MAX_LAYERS) {
        return LCD_ERROR_INVALID_LAYER;
    }

    LCD_Layer_DrawLine(layer_id, x1, y1, x2, y1, color);
    LCD_Layer_DrawLine(layer_id, x1, y1, x1, y2, color);
    LCD_Layer_DrawLine(layer_id, x1, y2, x2, y2, color);
    LCD_Layer_DrawLine(layer_id, x2, y1, x2, y2, color);

    return LCD_OK;
}

/**
 * @brief  在指定层绘制圆形
 * @param  layer_id: 层ID (0或1)
 * @param  x0: 圆心X坐标
 * @param  y0: 圆心Y坐标
 * @param  r: 半径
 * @param  color: 圆形颜色
 * @retval LCD_StatusTypeDef 状态码
 */
LCD_StatusTypeDef LCD_Layer_DrawCircle(uint8_t layer_id, uint16_t x0, uint16_t y0, uint8_t r, uint32_t color) {
    if (layer_id >= LCD_MAX_LAYERS) {
        return LCD_ERROR_INVALID_LAYER;
    }

    uint8_t old_layer = lcdltdc.activelayer;
    LTDC_Select_Layer(layer_id);

    // 使用Bresenham圆算法
    int a = 0, b = r;
    int di = 3 - (r << 1);
    while (a <= b) {
        LTDC_Draw_Point(x0 + a, y0 - b, color);
        LTDC_Draw_Point(x0 + b, y0 - a, color);
        LTDC_Draw_Point(x0 + b, y0 + a, color);
        LTDC_Draw_Point(x0 + a, y0 + b, color);
        LTDC_Draw_Point(x0 - a, y0 + b, color);
        LTDC_Draw_Point(x0 - b, y0 + a, color);
        LTDC_Draw_Point(x0 - b, y0 - a, color);
        LTDC_Draw_Point(x0 - a, y0 - b, color);
        a++;
        if (di < 0) {
            di += 4 * a + 6;
        } else {
            di += 10 + 4 * (a - b);
            b--;
        }
    }

    LTDC_Select_Layer(old_layer);

    return LCD_OK;
}

#endif // LCD_DUAL_LAYER_ENABLE

// ============================================================================
// 向后兼容的双层显示接口 (保持原有API)
// ============================================================================
void LCD_SwitchLayer(uint8_t layerx) {
    LTDC_Select_Layer(layerx);
}

void LCD_ClearLayer(uint8_t layerx, uint32_t color) {
    uint8_t old_layer = lcdltdc.activelayer;
    LTDC_Select_Layer(layerx);
    LTDC_Clear(color);
    LTDC_Select_Layer(old_layer);
}

void LCD_FillLayer(uint8_t layerx, uint16_t sx, uint16_t sy, uint16_t ex, uint16_t ey, uint32_t color) {
    uint8_t old_layer = lcdltdc.activelayer;
    LTDC_Select_Layer(layerx);
    LTDC_Fill(sx, sy, ex, ey, color);
    LTDC_Select_Layer(old_layer);
}

void LCD_DrawPointLayer(uint8_t layerx, uint16_t x, uint16_t y, uint32_t color) {
    uint8_t old_layer = lcdltdc.activelayer;
    LTDC_Select_Layer(layerx);
    LTDC_Draw_Point(x, y, color);
    LTDC_Select_Layer(old_layer);
}

// 基础绘图接口
void LCD_DrawPoint(uint16_t x, uint16_t y) {
    LTDC_Draw_Point(x, y, POINT_COLOR);
}

void LCD_Fast_DrawPoint(uint16_t x, uint16_t y, uint32_t color) {
    LTDC_Draw_Point(x, y, color);
}

void LCD_DrawLine(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2) {
    uint16_t t;
    int xerr = 0, yerr = 0, delta_x, delta_y, distance;
    int incx, incy, uRow, uCol;
    delta_x = x2 - x1;
    delta_y = y2 - y1;
    uRow = x1;
    uCol = y1;
    incx = (delta_x > 0) ? 1 : ((delta_x == 0) ? 0 : -1);
    if (delta_x < 0) delta_x = -delta_x;
    incy = (delta_y > 0) ? 1 : ((delta_y == 0) ? 0 : -1);
    if (delta_y < 0) delta_y = -delta_y;
    distance = (delta_x > delta_y) ? delta_x : delta_y;
    for (t = 0; t <= distance + 1; t++) {
        LCD_DrawPoint(uRow, uCol);
        xerr += delta_x;
        yerr += delta_y;
        if (xerr > distance) { xerr -= distance; uRow += incx; }
        if (yerr > distance) { yerr -= distance; uCol += incy; }
    }
}

uint32_t LCD_ReadPoint(uint16_t x, uint16_t y) {
    if (x >= lcdltdc.width || y >= lcdltdc.height)
        return 0;
    return LTDC_Read_Point(x, y);
}

void LCD_Fill(uint16_t sx, uint16_t sy, uint16_t ex, uint16_t ey, uint32_t color) {
    LTDC_Fill(sx, sy, ex, ey, color);
}

void LCD_Color_Fill(uint16_t sx, uint16_t sy, uint16_t ex, uint16_t ey, uint16_t *color) {
    LTDC_Color_Fill(sx, sy, ex, ey, color);
}

void LCD_DrawRectangle(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2) {
    LCD_DrawLine(x1, y1, x2, y1);
    LCD_DrawLine(x1, y1, x1, y2);
    LCD_DrawLine(x1, y2, x2, y2);
    LCD_DrawLine(x2, y1, x2, y2);
}

void LCD_Draw_Circle(uint16_t x0, uint16_t y0, uint8_t r) {
    int a = 0, b = r;
    int di = 3 - (r << 1);
    while (a <= b) {
        LCD_DrawPoint(x0 + a, y0 - b);
        LCD_DrawPoint(x0 + b, y0 - a);
        LCD_DrawPoint(x0 + b, y0 + a);
        LCD_DrawPoint(x0 + a, y0 + b);
        LCD_DrawPoint(x0 - a, y0 + b);
        LCD_DrawPoint(x0 - b, y0 + a);
        LCD_DrawPoint(x0 - a, y0 - b);
        LCD_DrawPoint(x0 - b, y0 - a);
        a++;
        if (di < 0) di += 4 * a + 6;
        else { di += 10 + 4 * (a - b); b--; }
    }
}

void LCD_ShowChar(uint16_t x, uint16_t y, uint8_t num, uint8_t size, uint8_t mode) {
    uint8_t temp, t1, t;
    uint16_t y0 = y;
    uint8_t csize = (size / 8 + ((size % 8) ? 1 : 0)) * (size / 2);
    num = num - ' ';
    for (t = 0; t < csize; t++) {
        if (size == 12) temp = asc2_1206[num][t];
        else if (size == 16) temp = asc2_1608[num][t];
        else if (size == 24) temp = asc2_2412[num][t];
        else if (size == 32) temp = asc2_3216[num][t];
        else return;
        for (t1 = 0; t1 < 8; t1++) {
            if (temp & 0x80) LCD_Fast_DrawPoint(x, y, POINT_COLOR);
            else if (mode == 0) LCD_Fast_DrawPoint(x, y, BACK_COLOR);
            temp <<= 1;
            y++;
            if (y >= lcdltdc.height) return;
            if ((y - y0) == size) {
                y = y0;
                x++;
                if (x >= lcdltdc.width) return;
                break;
            }
        }
    }
}

uint32_t LCD_Pow(uint8_t m, uint8_t n) {
    uint32_t result = 1;
    while (n--) result *= m;
    return result;
}

void LCD_ShowNum(uint16_t x, uint16_t y, uint32_t num, uint8_t len, uint8_t size) {
    uint8_t t, temp;
    uint8_t enshow = 0;
    for (t = 0; t < len; t++) {
        temp = (num / LCD_Pow(10, len - t - 1)) % 10;
        if (enshow == 0 && t < (len - 1)) {
            if (temp == 0) {
                LCD_ShowChar(x + (size / 2) * t, y, ' ', size, 0);
                continue;
            } else enshow = 1;
        }
        LCD_ShowChar(x + (size / 2) * t, y, temp + '0', size, 0);
    }
}

void LCD_ShowxNum(uint16_t x, uint16_t y, uint32_t num, uint8_t len, uint8_t size, uint8_t mode) {
    uint8_t t, temp;
    uint8_t enshow = 0;
    for (t = 0; t < len; t++) {
        temp = (num / LCD_Pow(10, len - t - 1)) % 10;
        if (enshow == 0 && t < (len - 1)) {
            if (temp == 0) {
                if (mode & 0x80) LCD_ShowChar(x + (size / 2) * t, y, '0', size, mode & 0x01);
                else LCD_ShowChar(x + (size / 2) * t, y, ' ', size, mode & 0x01);
                continue;
            } else enshow = 1;
        }
        LCD_ShowChar(x + (size / 2) * t, y, temp + '0', size, mode & 0x01);
    }
}

void LCD_ShowString(uint16_t x, uint16_t y, uint16_t width, uint16_t height, uint8_t size, uint8_t *p) {
    uint8_t x0 = x;
    width += x;
    height += y;
    while ((*p <= '~') && (*p >= ' ')) {
        if (x >= width) { x = x0; y += size; }
        if (y >= height) break;
        LCD_ShowChar(x, y, *p, size, 0);
        x += size / 2;
        p++;
    }
}

void LCD_ShowStr(uint16_t x, uint16_t y, uint8_t size, char *p) {
    uint8_t x0 = x;
    uint16_t width = x + strlen(p) * size / 2;
    uint16_t height = y + size;
    while ((*p <= '~') && (*p >= ' ')) {
        if (x >= width) { x = x0; y += size; }
        if (y >= height) break;
        LCD_ShowChar(x, y, *p, size, 0);
        x += size / 2;
        p++;
    }
}

#ifdef LCD_DUAL_LAYER_ENABLE
// ============================================================================
// 双层显示文本函数实现
// ============================================================================

/**
 * @brief  在指定层显示字符
 * @param  layer_id: 层ID (0或1)
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  ch: 要显示的字符
 * @param  size: 字体大小 (12/16/24/32)
 * @param  color: 字符颜色
 * @param  bg_color: 背景颜色
 * @retval LCD_StatusTypeDef 状态码
 */
LCD_StatusTypeDef LCD_Layer_ShowChar(uint8_t layer_id, uint16_t x, uint16_t y, uint8_t ch, uint8_t size, uint32_t color, uint32_t bg_color) {
    if (layer_id >= LCD_MAX_LAYERS) {
        return LCD_ERROR_INVALID_LAYER;
    }

    if (size != 12 && size != 16 && size != 24 && size != 32) {
        return LCD_ERROR_INVALID_PARAM;
    }

    uint8_t old_layer = lcdltdc.activelayer;
    uint32_t old_point_color = POINT_COLOR;
    uint32_t old_back_color = BACK_COLOR;

    LTDC_Select_Layer(layer_id);
    POINT_COLOR = color;
    BACK_COLOR = bg_color;

    uint8_t temp, t1, t;
    uint16_t y0 = y;
    uint8_t csize = (size / 8 + ((size % 8) ? 1 : 0)) * (size / 2);
    ch = ch - ' ';

    for (t = 0; t < csize; t++) {
        if (size == 12) temp = asc2_1206[ch][t];
        else if (size == 16) temp = asc2_1608[ch][t];
        else if (size == 24) temp = asc2_2412[ch][t];
        else if (size == 32) temp = asc2_3216[ch][t];
        else {
            POINT_COLOR = old_point_color;
            BACK_COLOR = old_back_color;
            LTDC_Select_Layer(old_layer);
            return LCD_ERROR_INVALID_PARAM;
        }

        for (t1 = 0; t1 < 8; t1++) {
            if (temp & 0x80) LTDC_Draw_Point(x, y, color);
            else LTDC_Draw_Point(x, y, bg_color);
            temp <<= 1;
            y++;
            if (y >= lcdltdc.height) break;
            if ((y - y0) == size) {
                y = y0;
                x++;
                if (x >= lcdltdc.width) break;
                break;
            }
        }
    }

    POINT_COLOR = old_point_color;
    BACK_COLOR = old_back_color;
    LTDC_Select_Layer(old_layer);

    return LCD_OK;
}

/**
 * @brief  在指定层显示字符串
 * @param  layer_id: 层ID (0或1)
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  str: 要显示的字符串
 * @param  size: 字体大小 (12/16/24/32)
 * @param  color: 字符颜色
 * @param  bg_color: 背景颜色
 * @retval LCD_StatusTypeDef 状态码
 */
LCD_StatusTypeDef LCD_Layer_ShowString(uint8_t layer_id, uint16_t x, uint16_t y, const char *str, uint8_t size, uint32_t color, uint32_t bg_color) {
    if (layer_id >= LCD_MAX_LAYERS) {
        return LCD_ERROR_INVALID_LAYER;
    }

    if (str == NULL) {
        return LCD_ERROR_INVALID_PARAM;
    }

    uint16_t x0 = x;

    while (*str != '\0') {
        if (*str >= ' ' && *str <= '~') {
            if (x + size/2 > lcdltdc.width) {
                x = x0;
                y += size;
                if (y + size > lcdltdc.height) break;
            }

            LCD_StatusTypeDef result = LCD_Layer_ShowChar(layer_id, x, y, *str, size, color, bg_color);
            if (result != LCD_OK) {
                return result;
            }

            x += size / 2;
        }
        str++;
    }

    return LCD_OK;
}

/**
 * @brief  在指定层显示数字
 * @param  layer_id: 层ID (0或1)
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  num: 要显示的数字
 * @param  len: 数字长度
 * @param  size: 字体大小 (12/16/24/32)
 * @param  color: 字符颜色
 * @retval LCD_StatusTypeDef 状态码
 */
LCD_StatusTypeDef LCD_Layer_ShowNum(uint8_t layer_id, uint16_t x, uint16_t y, uint32_t num, uint8_t len, uint8_t size, uint32_t color) {
    if (layer_id >= LCD_MAX_LAYERS) {
        return LCD_ERROR_INVALID_LAYER;
    }

    if (len == 0 || len > 10) {
        return LCD_ERROR_INVALID_PARAM;
    }

    uint8_t t, temp;
    uint8_t enshow = 0;

    for (t = 0; t < len; t++) {
        temp = (num / LCD_Pow(10, len - t - 1)) % 10;
        if (enshow == 0 && t < (len - 1)) {
            if (temp == 0) {
                LCD_StatusTypeDef result = LCD_Layer_ShowChar(layer_id, x + (size / 2) * t, y, ' ', size, color, 0x0000);
                if (result != LCD_OK) return result;
                continue;
            } else {
                enshow = 1;
            }
        }
        LCD_StatusTypeDef result = LCD_Layer_ShowChar(layer_id, x + (size / 2) * t, y, temp + '0', size, color, 0x0000);
        if (result != LCD_OK) return result;
    }

    return LCD_OK;
}

#endif  // LCD_DUAL_LAYER_ENABLE
