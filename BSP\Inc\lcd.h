
#ifndef INC_LCD_H_
#define INC_LCD_H_

#include "main.h"
#include "ltdc.h"

extern LTDC_HandleTypeDef hltdc;


/**
 * @brief 双层显示模式控制
 * @note  1: 启用双层模式 (Layer 0 + Layer 1)
 *        0: 单层模式 (仅Layer 0，保持向后兼容)
 */
#ifndef LCD_DUAL_LAYER_ENABLE
#define LCD_DUAL_LAYER_ENABLE           1
#endif

/**
 * @brief 调试模式控制
 * @note  1: 启用调试信息输出
 *        0: 禁用调试信息
 */
#ifndef LCD_DEBUG_ENABLE
#define LCD_DEBUG_ENABLE                0
#endif

/**
 * @brief DMA2D硬件加速控制
 * @note  1: 启用DMA2D硬件加速
 *        0: 使用软件实现
 */
#ifndef LCD_DMA2D_ENABLE
#define LCD_DMA2D_ENABLE                1
#endif

/**
 * @brief 参数检查配置
 * @note  1: 启用参数检查 (推荐用于调试)
 *        0: 禁用参数检查 (提高性能)
 */
#ifndef LCD_PARAM_CHECK_ENABLE
#define LCD_PARAM_CHECK_ENABLE          1
#endif

/**
 * @brief 错误回调配置
 */
#ifndef LCD_ERROR_CALLBACK_ENABLE
#define LCD_ERROR_CALLBACK_ENABLE       0
#endif

/**
 * @brief 向后兼容模式
 * @note  1: 保持与旧版本API的完全兼容
 *        0: 仅保持基本兼容性
 */
#ifndef LCD_BACKWARD_COMPATIBILITY
#define LCD_BACKWARD_COMPATIBILITY      1
#endif

/**
 * @brief CubeMX兼容模式
 * @note  1: 确保与STM32CubeMX生成的代码兼容
 *        0: 使用优化的初始化流程
 */
#ifndef LCD_CUBEMX_COMPATIBILITY
#define LCD_CUBEMX_COMPATIBILITY        1
#endif

// ============================================================================
// 硬件配置参数
// ============================================================================

/**
 * @brief LCD物理分辨率配置
 */
#define LCD_PHYSICAL_WIDTH              800
#define LCD_PHYSICAL_HEIGHT             480

/**
 * @brief LCD时序参数配置 (根据具体LCD面板调整)
 */
#define LCD_HSW                         1       // 水平同步宽度
#define LCD_VSW                         3       // 垂直同步宽度
#define LCD_HBP                         46      // 水平后沿
#define LCD_VBP                         23      // 垂直后沿
#define LCD_HFP                         40      // 水平前沿
#define LCD_VFP                         13      // 垂直前沿

/**
 * @brief 背光控制引脚配置
 */
#define LCD_BACKLIGHT_GPIO_PORT         GPIOD
#define LCD_BACKLIGHT_GPIO_PIN          GPIO_PIN_7
#define LCD_BACKLIGHT_ON_LEVEL          GPIO_PIN_SET
#define LCD_BACKLIGHT_OFF_LEVEL         GPIO_PIN_RESET

/**
 * @brief 帧缓冲区配置
 * @note  ".sdram_data" 是链接器段名称，用于将帧缓冲区放到SDRAM中
 */
#define LCD_SDRAM_BASE_ADDR             0xD0000000
#define LCD_PIXEL_SIZE                  2       // RGB565: 2字节/像素
#define LCD_FRAMEBUFFER_SIZE            (LCD_PHYSICAL_WIDTH * LCD_PHYSICAL_HEIGHT * LCD_PIXEL_SIZE)

/**
 * @brief DMA2D超时配置
 */
#define LCD_DMA2D_TIMEOUT               0x1FFFFF

// ============================================================================
// 双层显示配置参数
// ============================================================================

#ifdef LCD_DUAL_LAYER_ENABLE

/**
 * @brief 层配置参数
 */
#define LCD_LAYER0_DEFAULT_ALPHA        255     // Layer 0默认透明度 (不透明)
#define LCD_LAYER1_DEFAULT_ALPHA        128     // Layer 1默认透明度 (半透明)

/**
 * @brief 默认混合模式配置
 */
#define LCD_LAYER0_DEFAULT_BLEND        LCD_BLEND_MODE_NORMAL
#define LCD_LAYER1_DEFAULT_BLEND        LCD_BLEND_MODE_ALPHA

/**
 * @brief 层默认颜色配置
 */
#define LCD_LAYER0_DEFAULT_COLOR        0xFFFF  // 白色
#define LCD_LAYER1_DEFAULT_COLOR        0x0000  // 透明黑色

/**
 * @brief 层使能默认配置
 */
#define LCD_LAYER0_DEFAULT_ENABLE       1       // Layer 0默认启用
#define LCD_LAYER1_DEFAULT_ENABLE       0       // Layer 1默认禁用

#endif // LCD_DUAL_LAYER_ENABLE

// ============================================================================
// 调试配置
// ============================================================================

#ifdef LCD_DEBUG_ENABLE
#include <stdio.h>
#define LCD_DEBUG_PRINT(fmt, ...)      printf("[LCD DEBUG] " fmt "\r\n", ##__VA_ARGS__)
#define LCD_ERROR_PRINT(fmt, ...)      printf("[LCD ERROR] " fmt "\r\n", ##__VA_ARGS__)
#else
#define LCD_DEBUG_PRINT(fmt, ...)      ((void)0)
#define LCD_ERROR_PRINT(fmt, ...)      ((void)0)
#endif // LCD_DEBUG_ENABLE

// ============================================================================
// 编译时检查
// ============================================================================

#if LCD_PHYSICAL_WIDTH == 0 || LCD_PHYSICAL_HEIGHT == 0
#error "LCD物理分辨率配置错误"
#endif

#if LCD_PIXEL_SIZE != 2 && LCD_PIXEL_SIZE != 3 && LCD_PIXEL_SIZE != 4
#error "不支持的像素格式"
#endif

#ifdef LCD_DUAL_LAYER_ENABLE
#if !defined(LTDC)
#error "双层模式需要LTDC外设支持"
#endif
#endif

// 层定义
#ifdef LCD_DUAL_LAYER_ENABLE
#define LCD_LAYER_0             0
#define LCD_LAYER_1             1
#define LCD_MAX_LAYERS          2
#else
#define LCD_LAYER_0             0
#define LCD_MAX_LAYERS          1
#endif

// 层状态定义
#define LCD_LAYER_DISABLED      0
#define LCD_LAYER_ENABLED       1

// Alpha混合模式定义
#define LCD_BLEND_MODE_NORMAL   0  // 正常混合
#define LCD_BLEND_MODE_ALPHA    1  // Alpha混合
#define LCD_BLEND_MODE_MULTIPLY 2  // 乘法混合

// 错误代码定义
typedef enum {
    LCD_OK = 0,
    LCD_ERROR,
    LCD_ERROR_INVALID_LAYER,
    LCD_ERROR_INVALID_PARAM,
    LCD_ERROR_MEMORY_ALLOC,
    LCD_ERROR_HAL_FAILED
} LCD_StatusTypeDef;

// 层配置结构体
typedef struct {
    uint8_t  layer_id;          // 层ID (0或1)
    uint8_t  enabled;           // 层使能状态
    uint8_t  alpha;             // 层透明度 (0-255)
    uint8_t  blend_mode;        // 混合模式
    uint32_t fb_start_addr;     // 帧缓冲区起始地址
    uint16_t window_x0;         // 窗口起始X坐标
    uint16_t window_y0;         // 窗口起始Y坐标
    uint16_t window_x1;         // 窗口结束X坐标
    uint16_t window_y1;         // 窗口结束Y坐标
    uint32_t default_color;     // 默认颜色
} LCD_LayerConfigTypeDef;

// 增强的LTDC设备结构体
typedef struct
{
    uint32_t pwidth;            // 物理宽度
    uint32_t pheight;           // 物理高度
    uint16_t hsw;               // 水平同步宽度
    uint16_t vsw;               // 垂直同步宽度
    uint16_t hbp;               // 水平后沿
    uint16_t vbp;               // 垂直后沿
    uint16_t hfp;               // 水平前沿
    uint16_t vfp;               // 垂直前沿
    uint8_t  activelayer;       // 当前活动层
    uint8_t  dir;               // 显示方向
    uint16_t width;             // 逻辑宽度
    uint16_t height;            // 逻辑高度
    uint32_t pixsize;           // 像素大小
#ifdef LCD_DUAL_LAYER_ENABLE
    LCD_LayerConfigTypeDef layer_config[LCD_MAX_LAYERS]; // 层配置
    uint8_t dual_layer_enabled; // 双层模式使能标志
#endif
}_ltdc_dev;

extern _ltdc_dev lcdltdc;
extern LTDC_HandleTypeDef LTDC_Handler;
extern uint32_t POINT_COLOR;
extern uint32_t BACK_COLOR;

#define LCD_PIXEL_FORMAT_RGB565         0X02
#define LCD_PIXFORMAT   LCD_PIXEL_FORMAT_RGB565

// 颜色定义 (RGB565格式)
#define WHITE           0xFFFF
#define BLACK           0x0000
#define BLUE            0x001F
#define RED             0xF800
#define GREEN           0x07E0
#define YELLOW          0xFFE0
#define CYAN            0x07FF
#define MAGENTA         0xF81F
#define GRAY            0x7BEF
#define DARKGRAY        0x39E7
#define LIGHTGRAY       0xBDF7

// ============================================================================
// 基础LTDC函数声明
// ============================================================================
void LTDC_ParameterInit(void);
void LTDC_Clear(uint32_t color);
void LTDC_Display_Dir(uint8_t dir);
void LTDC_Select_Layer(uint8_t layerx);
void LTDC_Switch(uint8_t sw);

uint32_t LTDC_Read_Point(uint16_t x, uint16_t y);
void LTDC_Draw_Point(uint16_t x, uint16_t y, uint32_t color);
void LTDC_Fill(uint16_t sx, uint16_t sy, uint16_t ex, uint16_t ey, uint32_t color);
void LTDC_Color_Fill(uint16_t sx, uint16_t sy, uint16_t ex, uint16_t ey, uint16_t *color);

// ============================================================================
// 基础LCD函数声明 (向后兼容)
// ============================================================================
void LCD_Init(void);
void LCD_DrawPoint(uint16_t x, uint16_t y);
uint32_t LCD_ReadPoint(uint16_t x, uint16_t y);
void LCD_Fast_DrawPoint(uint16_t x, uint16_t y, uint32_t color);
void LCD_DrawLine(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2);
void LCD_Fill(uint16_t sx, uint16_t sy, uint16_t ex, uint16_t ey, uint32_t color);
void LCD_Color_Fill(uint16_t sx, uint16_t sy, uint16_t ex, uint16_t ey, uint16_t *color);
void LCD_DrawRectangle(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2);
void LCD_Draw_Circle(uint16_t x0, uint16_t y0, uint8_t r);
void LCD_ShowChar(uint16_t x, uint16_t y, uint8_t num, uint8_t size, uint8_t mode);
void LCD_ShowNum(uint16_t x, uint16_t y, uint32_t num, uint8_t len, uint8_t size);
void LCD_ShowxNum(uint16_t x, uint16_t y, uint32_t num, uint8_t len, uint8_t size, uint8_t mode);
void LCD_ShowString(uint16_t x, uint16_t y, uint16_t width, uint16_t height, uint8_t size, uint8_t *p);
void LCD_ShowStr(uint16_t x, uint16_t y, uint8_t size, char *p);

#ifdef LCD_DUAL_LAYER_ENABLE
// ============================================================================
// 双层显示核心管理函数
// ============================================================================
LCD_StatusTypeDef LCD_DualLayer_Init(void);
LCD_StatusTypeDef LCD_Layer_Enable(uint8_t layer_id);
LCD_StatusTypeDef LCD_Layer_Disable(uint8_t layer_id);
LCD_StatusTypeDef LCD_Layer_SetAlpha(uint8_t layer_id, uint8_t alpha);
LCD_StatusTypeDef LCD_Layer_SetBlendMode(uint8_t layer_id, uint8_t blend_mode);
LCD_StatusTypeDef LCD_Layer_SetWindow(uint8_t layer_id, uint16_t x0, uint16_t y0, uint16_t x1, uint16_t y1);
LCD_StatusTypeDef LCD_Layer_SetDefaultColor(uint8_t layer_id, uint32_t color);
LCD_StatusTypeDef LCD_Layer_ConfigUpdate(uint8_t layer_id);

// ============================================================================
// 双层显示绘图函数
// ============================================================================
LCD_StatusTypeDef LCD_Layer_Clear(uint8_t layer_id, uint32_t color);
LCD_StatusTypeDef LCD_Layer_Fill(uint8_t layer_id, uint16_t sx, uint16_t sy, uint16_t ex, uint16_t ey, uint32_t color);
LCD_StatusTypeDef LCD_Layer_DrawPoint(uint8_t layer_id, uint16_t x, uint16_t y, uint32_t color);
LCD_StatusTypeDef LCD_Layer_DrawLine(uint8_t layer_id, uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint32_t color);
LCD_StatusTypeDef LCD_Layer_DrawRectangle(uint8_t layer_id, uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint32_t color);
LCD_StatusTypeDef LCD_Layer_DrawCircle(uint8_t layer_id, uint16_t x0, uint16_t y0, uint8_t r, uint32_t color);

// ============================================================================
// 双层显示文本函数
// ============================================================================
LCD_StatusTypeDef LCD_Layer_ShowChar(uint8_t layer_id, uint16_t x, uint16_t y, uint8_t ch, uint8_t size, uint32_t color, uint32_t bg_color);
LCD_StatusTypeDef LCD_Layer_ShowString(uint8_t layer_id, uint16_t x, uint16_t y, const char *str, uint8_t size, uint32_t color, uint32_t bg_color);
LCD_StatusTypeDef LCD_Layer_ShowNum(uint8_t layer_id, uint16_t x, uint16_t y, uint32_t num, uint8_t len, uint8_t size, uint32_t color);

// ============================================================================
// 双层显示实用函数
// ============================================================================
LCD_StatusTypeDef LCD_Layer_Copy(uint8_t src_layer, uint8_t dst_layer, uint16_t sx, uint16_t sy, uint16_t ex, uint16_t ey);
LCD_StatusTypeDef LCD_Layer_Blend(uint8_t layer1, uint8_t layer2, uint8_t blend_ratio);
uint8_t LCD_Layer_IsEnabled(uint8_t layer_id);
uint8_t LCD_Layer_GetAlpha(uint8_t layer_id);

// 向后兼容的双层接口 (保持原有API)
void LCD_SwitchLayer(uint8_t layerx);
void LCD_ClearLayer(uint8_t layerx, uint32_t color);
void LCD_FillLayer(uint8_t layerx, uint16_t sx, uint16_t sy, uint16_t ex, uint16_t ey, uint32_t color);
void LCD_DrawPointLayer(uint8_t layerx, uint16_t x, uint16_t y, uint32_t color);

#else
// 单层模式下的兼容性宏定义
#define LCD_Layer_Clear(layer_id, color)                    LCD_Fill(0, 0, lcdltdc.width-1, lcdltdc.height-1, color)
#define LCD_Layer_DrawPoint(layer_id, x, y, color)          LCD_Fast_DrawPoint(x, y, color)
#define LCD_Layer_Fill(layer_id, sx, sy, ex, ey, color)     LCD_Fill(sx, sy, ex, ey, color)

// 向后兼容的双层接口宏定义 (单层模式下忽略layer参数)
#define LCD_SwitchLayer(layerx)                             ((void)0)
#define LCD_ClearLayer(layerx, color)                       LTDC_Clear(color)
#define LCD_FillLayer(layerx, sx, sy, ex, ey, color)        LTDC_Fill(sx, sy, ex, ey, color)
#define LCD_DrawPointLayer(layerx, x, y, color)             LCD_Fast_DrawPoint(x, y, color)

#endif // LCD_DUAL_LAYER_ENABLE

#endif /* INC_LCD_H_ */