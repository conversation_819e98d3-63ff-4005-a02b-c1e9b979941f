{"artifacts": [{"path": "Fatfs.elf"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_options", "target_link_libraries", "target_compile_options", "target_include_directories", "target_sources"], "files": ["CMakeLists.txt", "cmake/stm32cubemx/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 38, "parent": 0}, {"command": 1, "file": 0, "line": 83, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 115, "parent": 3}, {"command": 3, "file": 0, "line": 77, "parent": 0}, {"command": 4, "file": 0, "line": 61, "parent": 0}, {"command": 5, "file": 1, "line": 109, "parent": 3}, {"command": 5, "file": 0, "line": 49, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11"}, {"backtrace": 5, "fragment": "-mcpu=cortex-m7"}, {"backtrace": 5, "fragment": "-mfloat-abi=hard"}, {"backtrace": 5, "fragment": "-mfpu=fpv5-sp-d16"}, {"backtrace": 5, "fragment": "-mthumb"}], "defines": [{"backtrace": 4, "define": "DEBUG"}, {"backtrace": 4, "define": "STM32F767xx"}, {"backtrace": 4, "define": "USE_HAL_DRIVER"}], "includes": [{"backtrace": 6, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc"}, {"backtrace": 4, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc"}, {"backtrace": 4, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target"}, {"backtrace": 4, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App"}, {"backtrace": 4, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc"}, {"backtrace": 4, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy"}, {"backtrace": 4, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src"}, {"backtrace": 4, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include"}, {"backtrace": 4, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include"}], "language": "C", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 19]}, {"compileCommandFragments": [{"fragment": " -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -x assembler-with-cpp -MMD -MP -g"}, {"backtrace": 5, "fragment": "-mcpu=cortex-m7"}, {"backtrace": 5, "fragment": "-mfloat-abi=hard"}, {"backtrace": 5, "fragment": "-mfpu=fpv5-sp-d16"}, {"backtrace": 5, "fragment": "-mthumb"}], "defines": [{"backtrace": 4, "define": "DEBUG"}, {"backtrace": 4, "define": "STM32F767xx"}, {"backtrace": 4, "define": "USE_HAL_DRIVER"}], "includes": [{"backtrace": 6, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/BSP/Inc"}, {"backtrace": 4, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc"}, {"backtrace": 4, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target"}, {"backtrace": 4, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App"}, {"backtrace": 4, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc"}, {"backtrace": 4, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy"}, {"backtrace": 4, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src"}, {"backtrace": 4, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include"}, {"backtrace": 4, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include"}], "language": "ASM", "sourceIndexes": [14]}], "dependencies": [{"backtrace": 4, "id": "STM32_Drivers::@768a070a0fe75716b479"}, {"backtrace": 4, "id": "FatFs::@768a070a0fe75716b479"}], "id": "Fatfs::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 2, "fragment": "-mcpu=cortex-m7", "role": "flags"}, {"backtrace": 2, "fragment": "-mfloat-abi=hard", "role": "flags"}, {"backtrace": 2, "fragment": "-mfpu=fpv5-sp-d16", "role": "flags"}, {"backtrace": 2, "fragment": "-mthumb", "role": "flags"}], "language": "C"}, "name": "Fatfs", "nameOnDisk": "Fatfs.elf", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 19]}, {"name": "", "sourceIndexes": [14]}, {"name": "Object Libraries", "sourceIndexes": [20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50]}], "sources": [{"backtrace": 7, "compileGroupIndex": 0, "path": "FATFS/App/fatfs.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "FATFS/Target/user_diskio.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/main.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/gpio.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/dma2d.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/fmc.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/i2c.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/ltdc.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/quadspi.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/rtc.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/stm32f7xx_it.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/stm32f7xx_hal_msp.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/sysmem.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/syscalls.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 1, "path": "startup_stm32f767xx.s", "sourceGroupIndex": 1}, {"backtrace": 8, "compileGroupIndex": 0, "path": "BSP/Src/W25QXX.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "BSP/Src/lcd.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "BSP/Src/lcd_test.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "BSP/Src/touch.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "BSP/Src/ff_opera_optimized.c", "sourceGroupIndex": 0}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Core/Src/system_stm32f7xx.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_cortex.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma2d.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rcc.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rcc_ex.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_flash.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_flash_ex.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_gpio.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma_ex.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_pwr.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_pwr_ex.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_i2c.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_i2c_ex.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_exti.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_ll_fmc.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_nor.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_sram.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_nand.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_sdram.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_ltdc.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_ltdc_ex.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dsi.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_qspi.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rtc.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rtc_ex.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/FatFs.dir/__/__/Middlewares/Third_Party/FatFs/src/diskio.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/FatFs.dir/__/__/Middlewares/Third_Party/FatFs/src/ff.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/FatFs.dir/__/__/Middlewares/Third_Party/FatFs/src/ff_gen_drv.c.obj", "sourceGroupIndex": 2}, {"backtrace": 4, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/FatFs.dir/__/__/Middlewares/Third_Party/FatFs/src/option/syscall.c.obj", "sourceGroupIndex": 2}], "type": "EXECUTABLE"}