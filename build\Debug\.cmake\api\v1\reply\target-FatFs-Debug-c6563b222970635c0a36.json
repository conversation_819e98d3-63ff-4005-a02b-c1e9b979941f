{"artifacts": [{"path": "cmake/stm32cubemx/CMakeFiles/FatFs.dir/./__/__/Middlewares/Third_Party/FatFs/src/diskio.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/FatFs.dir/./__/__/Middlewares/Third_Party/FatFs/src/ff.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/FatFs.dir/./__/__/Middlewares/Third_Party/FatFs/src/ff_gen_drv.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/FatFs.dir/./__/__/Middlewares/Third_Party/FatFs/src/option/syscall.c.obj"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_sources"], "files": ["cmake/stm32cubemx/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 104, "parent": 0}, {"command": 1, "file": 0, "line": 106, "parent": 0}, {"command": 2, "file": 0, "line": 105, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11"}], "defines": [{"backtrace": 2, "define": "DEBUG"}, {"backtrace": 2, "define": "STM32F767xx"}, {"backtrace": 2, "define": "USE_HAL_DRIVER"}], "includes": [{"backtrace": 2, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Core/Inc"}, {"backtrace": 2, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/Target"}, {"backtrace": 2, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/FATFS/App"}, {"backtrace": 2, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc"}, {"backtrace": 2, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/STM32F7xx_HAL_Driver/Inc/Legacy"}, {"backtrace": 2, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Middlewares/Third_Party/FatFs/src"}, {"backtrace": 2, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Device/ST/STM32F7xx/Include"}, {"backtrace": 2, "path": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/Drivers/CMSIS/Include"}], "language": "C", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0, 1, 2, 3]}], "id": "FatFs::@768a070a0fe75716b479", "name": "FatFs", "paths": {"build": "cmake/stm32cubemx", "source": "cmake/stm32cubemx"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "Middlewares/Third_Party/FatFs/src/diskio.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Middlewares/Third_Party/FatFs/src/ff.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Middlewares/Third_Party/FatFs/src/ff_gen_drv.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Middlewares/Third_Party/FatFs/src/option/syscall.c", "sourceGroupIndex": 0}], "type": "OBJECT_LIBRARY"}