//
// Created by wang on 2025/7/14.
//

#ifndef DEMO_RGB_LCD_KEYLED_H
#define DEMO_RGB_LCD_KEYLED_H

#include"main.h"


typedef enum {
    KEY_NONE = 0,
    KEY_1,
    KEY_2,
    KEY_3,
    KEY_4
} KEYS;

#define KEY_WAIT_ALWAYS		0

KEYS ScanPressedKey(uint32_t timeout);


#ifdef	LED_R_Pin //RGB LED
#define LED_R_Toggle()	    HAL_GPIO_TogglePin(LED_R_GPIO_Port, LED_R_Pin)
	#define LED_R_OFF()		HAL_GPIO_WritePin(LED_R_GPIO_Port, LED_R_Pin, GPIO_PIN_SET)
	#define LED_R_ON()		HAL_GPIO_WritePin(LED_R_GPIO_Port, LED_R_Pin, GPIO_PIN_RESET)
#endif

#ifdef	LED_G_Pin //RGB LED
#define LED_G_Toggle()	    HAL_GPIO_TogglePin(LED_G_GPIO_Port, LED_G_Pin)
	#define LED_G_OFF()		HAL_GPIO_WritePin(LED_G_GPIO_Port, LED_G_Pin, GPIO_PIN_SET)
	#define LED_G_ON()		HAL_GPIO_WritePin(LED_G_GPIO_Port, LED_G_Pin, GPIO_PIN_RESET)
#endif

#ifdef	LED_B_Pin //RGB LED
#define LED_B_Toggle()	    HAL_GPIO_TogglePin(LED_B_GPIO_Port, LED_B_Pin)
	#define LED_B_OFF()		HAL_GPIO_WritePin(LED_B_GPIO_Port, LED_B_Pin, GPIO_PIN_SET)
	#define LED_B_ON()		HAL_GPIO_WritePin(LED_B_GPIO_Port, LED_B_Pin, GPIO_PIN_RESET)
#endif

#ifdef	Buzzer_Pin //蜂鸣器
#define Buzzer_Toggle()	    HAL_GPIO_TogglePin(Buzzer_GPIO_Port, Buzzer_Pin)
	#define Buzzer_ON()		HAL_GPIO_WritePin(Buzzer_GPIO_Port, Buzzer_Pin, GPIO_PIN_SET)
	#define Buzzer_OFF()	HAL_GPIO_WritePin(Buzzer_GPIO_Port, Buzzer_Pin, GPIO_PIN_RESET)
#endif
#endif //DEMO_RGB_LCD_KEYLED_H
