{"inputs": [{"path": "CMakeLists.txt"}, {"path": "cmake/gcc-arm-none-eabi.cmake"}, {"isGenerated": true, "path": "build/Debug/CMakeFiles/3.31.8/CMakeSystem.cmake"}, {"path": "cmake/gcc-arm-none-eabi.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isGenerated": true, "path": "build/Debug/CMakeFiles/3.31.8/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "build/Debug/CMakeFiles/3.31.8/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Platform/Generic.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Compiler/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Platform/Generic.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Internal/CMakeCLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Platform/Generic.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Internal/CMakeCXXLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isGenerated": true, "path": "build/Debug/CMakeFiles/3.31.8/CMakeASMCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeASMInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Compiler/GNU-ASM.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Internal/CMakeASMLinkerInformation.cmake"}, {"path": "cmake/stm32cubemx/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/CMakeASMInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Compiler/GNU-ASM.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/STM32cube/STM32cubeide/STM32CubeCLT_1.18.0/CMake/share/cmake-3.31/Modules/Compiler/GNU.cmake"}], "kind": "cmakeFiles", "paths": {"build": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs/build/Debug", "source": "F:/STM32cube/STM32-Clion/F767_HAL/DEMO6_Fatfs"}, "version": {"major": 1, "minor": 1}}