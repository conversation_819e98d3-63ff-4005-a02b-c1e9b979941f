# W25QXX Flash存储器驱动库

## 概述

W25QXX系列Flash存储器驱动库，支持多种型号和通信接口。提供完整的读写、擦除功能，支持QSPI和SPI两种通信模式。

## 支持的芯片型号

| 型号 | 容量 | 芯片ID | 页大小 | 页数量 | 扇区大小 | 扇区数量 | 块大小 | 块数量 | 地址模式 |
|------|------|--------|--------|--------|----------|----------|--------|--------|----------|
| W25Q80 | 1MB | 0xEF13 | 256B | 4,096 | 4KB | 256 | 64KB | 16 | 3字节 |
| W25Q16 | 2MB | 0xEF14 | 256B | 8,192 | 4KB | 512 | 64KB | 32 | 3字节 |
| W25Q32 | 4MB | 0xEF15 | 256B | 16,384 | 4KB | 1,024 | 64KB | 64 | 3字节 |
| W25Q64 | 8MB | 0xEF16 | 256B | 32,768 | 4KB | 2,048 | 64KB | 128 | 3字节 |
| W25Q128 | 16MB | 0xEF17 | 256B | 65,536 | 4KB | 4,096 | 64KB | 256 | 3字节 |
| W25Q256 | 32MB | 0xEF18 | 256B | 131,072 | 4KB | 8,192 | 64KB | 512 | 4字节 |

## 存储结构说明

### 基本单位
- **页（Page）**：最小编程单位，256字节
- **扇区（Sector）**：最小擦除单位，4KB（16页）
- **块（Block）**：大擦除单位，64KB（16扇区）

### 地址模式
- **3字节地址**：W25Q80~W25Q128（≤16MB）
- **4字节地址**：W25Q256（32MB）

## 通信接口

### 支持的接口
- **QSPI模式**：高速四线SPI接口
- **SPI1模式**：标准SPI接口（CS: PA4）
- **SPI2模式**：标准SPI接口（CS: PB12）

### 接口配置
```c
// 在W25QXX.h中选择接口
#define W25QXX_INTERFACE    W25QXX_USE_QSPI    // QSPI模式
// #define W25QXX_INTERFACE    W25QXX_USE_SPI1   // SPI1模式
// #define W25QXX_INTERFACE    W25QXX_USE_SPI2   // SPI2模式
```

### 芯片型号配置
```c
// 在W25QXX.h中选择芯片型号（只能选择一种）
#define USE_W25Q80          0
#define USE_W25Q16          0
#define USE_W25Q32          0
#define USE_W25Q64          0
#define USE_W25Q128         0
#define USE_W25Q256         1    // 当前选择W25Q256
```

## 快速开始

### 1. 基础初始化
```c
#include "W25QXX.h"

int main(void)
{
    // 系统初始化
    HAL_Init();
    SystemClock_Config();
    MX_GPIO_Init();
    MX_QSPI_Init();  // 或 MX_SPI1_Init() / MX_SPI2_Init()
    
    // W25QXX初始化
    if (W25QXX_Init() != HAL_OK) {
        Error_Handler();
    }
    
    // 读取芯片ID验证
    uint16_t chip_id = W25QXX_ReadID();
    if (chip_id != W25QXX_CHIP_ID) {
        Error_Handler();
    }
    
    while (1) {
        // 应用代码
    }
}
```

### 2. 基础读写操作
```c
uint8_t write_data[256] = {0x01, 0x02, 0x03, ...};
uint8_t read_data[256];
uint32_t address = 0x000000;

// 擦除扇区（必须先擦除才能写入）
W25QXX_EraseSector(address);

// 写入数据
W25QXX_WritePage(address, write_data, 256);

// 读取数据
W25QXX_ReadData(address, read_data, 256);
```

## API参考

### 初始化和控制
| 函数 | 功能 | 返回值 |
|------|------|--------|
| `W25QXX_Init()` | 初始化芯片 | `HAL_StatusTypeDef` |
| `W25QXX_ReadID()` | 读取芯片ID | `uint16_t` |
| `W25QXX_ReadJedecID()` | 读取JEDEC ID | `uint32_t` |
| `W25QXX_PowerDown()` | 进入低功耗模式 | `HAL_StatusTypeDef` |
| `W25QXX_WakeUp()` | 唤醒芯片 | `HAL_StatusTypeDef` |

### 读取操作
| 函数 | 功能 | 参数 |
|------|------|------|
| `W25QXX_ReadData()` | 普通读取 | 地址、缓冲区、长度 |
| `W25QXX_FastRead()` | 快速读取 | 地址、缓冲区、长度 |

### 写入操作
| 函数 | 功能 | 参数 |
|------|------|------|
| `W25QXX_WritePage()` | 页编程（≤256字节） | 地址、缓冲区、长度 |
| `W25QXX_WriteData()` | 跨页写入 | 地址、缓冲区、长度 |

### 擦除操作
| 函数 | 功能 | 参数 |
|------|------|------|
| `W25QXX_EraseSector()` | 擦除扇区（4KB） | 扇区地址 |
| `W25QXX_EraseBlock()` | 擦除块（64KB） | 块地址 |
| `W25QXX_EraseChip()` | 擦除整个芯片 | 无 |

## 使用注意事项

### 1. 擦除规则
- **写入前必须擦除**：Flash只能从1写为0，擦除将所有位设为1
- **擦除单位**：最小4KB扇区，推荐64KB块擦除（速度更快）
- **擦除时间**：扇区擦除~400ms，块擦除~2s，整片擦除~40s

### 2. 写入限制
- **页边界**：单次写入不能跨越256字节页边界
- **写入长度**：使用`W25QXX_WritePage()`最多256字节
- **跨页写入**：使用`W25QXX_WriteData()`自动处理页边界

### 3. 地址计算
```c
// 扇区地址计算（4KB对齐）
uint32_t sector_addr = address & 0xFFFFF000;

// 块地址计算（64KB对齐）
uint32_t block_addr = address & 0xFFFF0000;

// 页地址计算（256B对齐）
uint32_t page_addr = address & 0xFFFFFF00;
```

### 4. 性能优化
- **使用QSPI模式**：比SPI模式快4倍
- **块擦除优先**：64KB块擦除比16个扇区擦除快
- **批量操作**：减少命令开销

## 错误处理

### 常见问题
1. **芯片ID读取失败**：检查硬件连接和时钟配置
2. **写入失败**：确认已正确擦除目标区域
3. **读取数据错误**：检查地址是否越界

### 调试方法
```c
// 检查芯片连接
uint16_t id = W25QXX_ReadID();
printf("Chip ID: 0x%04X (Expected: 0x%04X)\n", id, W25QXX_CHIP_ID);

// 检查状态寄存器
uint8_t status = W25QXX_ReadStatusReg1();
printf("Status: 0x%02X (Busy: %d)\n", status, status & 0x01);
```

## 硬件连接

### QSPI接口
| 信号 | STM32引脚 | 功能 |
|------|-----------|------|
| CLK  | PB2       | 时钟 |
| NCS  | PB6       | 片选 |
| IO0  | PD11      | 数据0 |
| IO1  | PD12      | 数据1 |
| IO2  | PE2       | 数据2 |
| IO3  | PD13      | 数据3 |

### SPI接口
| 信号 | SPI1引脚 | SPI2引脚 | 功能 |
|------|----------|----------|------|
| CLK  | PA5      | PB13     | 时钟 |
| MISO | PA6      | PB14     | 主入从出 |
| MOSI | PA7      | PB15     | 主出从入 |
| CS   | PA4      | PB12     | 片选 |

## 版本信息

- **当前版本**：V2.0
- **更新日期**：2025-01-27
- **主要特性**：
  - 支持W25Q80~W25Q256全系列
  - QSPI和SPI双模式支持
  - 自动4字节地址模式切换
  - 完整的读写擦除功能

## 许可证

本项目采用MIT许可证。
